---
// 文章轮播组件示例
import { getCollection } from 'astro:content';
import { themeConfig } from '~/.config';
import GenericCarousel from './GenericCarousel.astro';
import { formatDate } from '~/utils';

// 获取轮播配置
const carouselConfig = themeConfig.carousel || {};
const maxItems = carouselConfig.maxItems || 5; // 默认显示5个轮播项
const autoplay = carouselConfig.autoplay !== false;
const autoplaySpeed = carouselConfig.autoplaySpeed || 5000;

// 获取所有非草稿文章
const allPosts = await getCollection('posts', ({ data }) => {
  return import.meta.env.PROD ? !data.draft : true;
});

// 按发布日期排序（最新的在前）
const sortedPosts = allPosts
  .sort((a, b) => new Date(b.data.pubDate).getTime() - new Date(a.data.pubDate).getTime())
  .slice(0, maxItems);
---

<div class="posts-carousel">
  <h2 class="section-title">最新文章</h2>

  <GenericCarousel
    id="posts-carousel"
    items={sortedPosts}
    autoplay={autoplay}
    autoplaySpeed={autoplaySpeed}
  >
    {sortedPosts.map((post, index) => (
      <div class="post-card" slot={`item-${index}`}>
        {post.data.banner && (
          <div class="post-cover">
            <img src={post.data.banner.src} alt={post.data.title} loading="lazy" />
          </div>
        )}

        <div class="post-content">
          <h3 class="post-title">
            <a href={`/posts/${post.data.slug || post.id}/`}>{post.data.title}</a>
          </h3>

          <div class="post-meta">
            <span class="post-date">{formatDate(post.data.pubDate)}</span>
            {post.data.categories && post.data.categories.length > 0 && (
              <span class="post-category">{post.data.categories[0]}</span>
            )}
          </div>

          <p class="post-excerpt">
            {post.data.description || post.data.summary || ''}
          </p>

          <a href={`/posts/${post.data.slug || post.id}/`} class="read-more">
            阅读全文 <span class="i-mdi-arrow-right"></span>
          </a>
        </div>
      </div>
    ))}
  </GenericCarousel>
</div>

<style>
  .posts-carousel {
    margin-bottom: 3rem;
  }

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--uno-colors-primary);
    text-align: center;
    position: relative;
  }

  .section-title::after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background-color: var(--uno-colors-primary);
    margin: 0.5rem auto 0;
    border-radius: 3px;
  }

  .post-card {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 28px rgba(0, 0, 0, 0.1);
  }

  .post-cover {
    width: 100%;
    height: 180px;
    overflow: hidden;
  }

  .post-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .post-card:hover .post-cover img {
    transform: scale(1.05);
  }

  .post-content {
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  .post-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.3;
  }

  .post-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .post-title a:hover {
    color: var(--uno-colors-primary);
  }

  .post-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.75rem;
  }

  .post-excerpt {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #555;
    margin-bottom: 1rem;
    flex-grow: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .read-more {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--uno-colors-primary);
    text-decoration: none;
    transition: all 0.2s ease;
    align-self: flex-start;
  }

  .read-more:hover {
    gap: 0.5rem;
    opacity: 0.9;
  }

  /* 暗色模式 */
  :global(.dark) .post-card {
    background-color: #222222;
  }

  :global(.dark) .post-title a {
    color: #e5e7eb;
  }

  :global(.dark) .post-meta {
    color: #aaa;
  }

  :global(.dark) .post-excerpt {
    color: #bbb;
  }

  /* 响应式 */
  @media (max-width: 768px) {
    .post-cover {
      height: 150px;
    }

    .post-content {
      padding: 1rem;
    }

    .post-title {
      font-size: 1.1rem;
    }
  }
</style>
