---
# 必填字段
title: "文章标题"                      # 文章标题（必填）
pubDate: 2023-11-15                   # 发布日期（必填，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS）
categories: ["分类1", "分类2"]         # 文章分类（必填，至少一个分类）

# 可选基本信息
description: "文章的简短描述，用于SEO" # 文章描述（推荐填写，利于SEO）
summary: "文章的详细摘要，用于文章列表显示" # 文章摘要（可选，如果不填写则自动截取正文）
tags: ["标签1", "标签2", "标签3"]      # 文章标签（可选）
author: "作者名称"                     # 文章作者（可选，默认使用站点配置中的作者）
modDate: 2023-11-20                   # 最后修改日期（可选，格式同pubDate）

# 文章状态控制
draft: false                          # 是否为草稿（可选，true=草稿，生产环境不显示）
pin: false                            # 是否置顶（可选，true=置顶）
pinOrder: 0                           # 置顶顺序（可选，数字越小越靠前，默认为0）

# 文章显示控制
banner: ./images/banner.jpg           # 文章banner图片（可选，路径相对于文章所在目录）
slug: "custom-article-url"            # 自定义URL路径（可选，影响取决于路由配置）

# 系列文章设置（可选）
series:
  name: "系列名称"                     # 系列名称
  order: 1                            # 在系列中的顺序
  status: "连载中"                      # 系列状态（可选，默认为连载中，可选值：连载中、已完结、计划中、暂停更新）

# 外部链接和引用
commentsUrl: "https://example.com/comments/123" # 外部评论链接（可选）
source:                               # 文章来源（可选）
  url: "https://original-source.com/article"
  title: "原文标题"

# RSS相关（用于播客等）
enclosure:                            # RSS附件（可选，用于播客等）
  url: "https://example.com/podcast.mp3"
  length: 12345678
  type: "audio/mpeg"

# 自定义数据
customData: "任何自定义信息"           # 自定义数据字段（可选）




---

<!-- 这里开始编写文章内容 -->

# 文章标题

这里是文章的正文内容...

## 二级标题

### 三级标题

- 列表项1
- 列表项2
- 列表项3

```javascript
// 代码示例
function hello() {
  console.log("Hello, world!");
}
```

> 这是一段引用文本

[链接文本](https://example.com)

![图片描述](./images/example.jpg)

**粗体文本** 和 *斜体文本*

| 表头1 | 表头2 | 表头3 |
|-------|-------|-------|
| 单元格1 | 单元格2 | 单元格3 |
| 单元格4 | 单元格5 | 单元格6 |

1. 有序列表项1
2. 有序列表项2
3. 有序列表项3

---

## 注意事项

1. 图片路径相对于文章所在目录
2. 中文分类和标签会自动映射为英文URL（如果在配置中设置了映射）
3. 系列文章会自动生成导航和链接
4. 置顶文章会显示在首页顶部
