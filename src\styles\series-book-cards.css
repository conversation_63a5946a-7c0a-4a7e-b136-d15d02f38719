/**
 * 系列页面书本/杂志封面卡片样式
 * 为系列列表页面提供时尚美观的书本/杂志封面卡片布局
 */

/* 系列列表容器 - 使用网格布局 */
.series-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
  width: 100%;
}

/* 书本/杂志封面卡片基本样式 */
.book-card {
  position: relative;
  /* 使用宽高比例控制高度，标准书籍比例约为1.5:1（高:宽） */
  aspect-ratio: 0.7; /* 宽:高 = 0.7:1，即高:宽 = 1.43:1 */
  border-radius: 8px;
  overflow: visible;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  perspective: 1000px;
  box-shadow: none !important; /* 确保没有阴影 */
}

.book-card:hover .book-cover {
  transform: translateY(-10px) rotateY(-15deg);
  box-shadow:
    15px 10px 20px rgba(0, 0, 0, 0.2),
    15px 6px 6px rgba(0, 0, 0, 0.1),
    5px 10px 10px -5px rgba(0, 0, 0, 0.1);
}

/* 书本卡片作为链接的样式 */
.book-card {
  display: block;
  text-decoration: none;
  color: inherit;
  background: transparent !important; /* 强制不显示背景色 */
  border: none !important; /* 确保没有边框 */
}

/* 确保悬停和焦点状态下也不显示背景色 */
.book-card:hover,
.book-card:focus,
.book-card:active {
  background: transparent !important;
  text-decoration: none;
  outline: none;
}

/* 书本封面 */
.book-cover {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: var(--uno-colors-primary, #2e405b);
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.15),
    0 6px 6px rgba(0, 0, 0, 0.1),
    -10px 10px 10px -5px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  transform-style: preserve-3d;
  transform-origin: left center;
  transition: transform 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);
  overflow: hidden;
}

/* 书本内容容器 */
.book-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
}

/* 书本标题和状态的容器 */
.book-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 0.5rem;
  margin-bottom: 0.3rem;
}

/* 书脊效果 */
.book-cover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  transform: translateX(-10px) rotateY(-20deg);
  transform-origin: left center;
  z-index: -1;
}

/* 书页效果 */
.book-cover::after {
  content: '';
  position: absolute;
  top: 5px;
  right: 5px;
  bottom: 5px;
  left: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  z-index: -1;
}

/* 书本标题 */
.book-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #fff;
  margin: 0;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
  /* 添加多行文本截断 */
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
}

/* 书本元数据 */
.book-meta {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.9);
  margin-top: auto;
  position: relative;
  z-index: 1;
}

.book-meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.book-meta-icon {
  opacity: 0.9;
}

/* 书本状态标签 */
.book-status {
  padding: 0.25rem 0.6rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 20; /* 确保状态标签在卡片链接之上 */
  flex-shrink: 0;
  white-space: nowrap;
}

/* 不同状态的样式 */
.status-completed {
  background-color: #10b981;
  color: white;
}

.status-ongoing {
  background-color: #3b82f6;
  color: white;
}

.status-paused {
  background-color: #f59e0b;
  color: white;
}

.status-planned {
  background-color: #6b7280;
  color: white;
}

/* 装饰元素 */
.book-decoration {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  width: 60px;
  height: 60px;
  opacity: 0.1;
  z-index: 0;
}

/* 使用主题色作为封面颜色 */
.book-cover {
  background-image: linear-gradient(135deg,
    rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.9) 0%,
    rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.7) 100%);
}

/* 暗色模式适配 */
.dark .book-card {
  box-shadow: none !important; /* 确保暗色模式下也没有阴影 */
}

.dark .book-cover::after {
  background: rgba(255, 255, 255, 0.03);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .series-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1.25rem;
  }

  .book-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .series-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
  }

  .book-cover {
    padding: 1.25rem;
  }

  .book-title {
    font-size: 1.1rem;
  }

  .book-meta {
    font-size: 0.8rem;
    gap: 0.3rem;
  }

  .book-status {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
  }

  .book-header {
    gap: 0.4rem;
  }
}

@media (max-width: 480px) {
  .series-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 0.75rem;
  }

  .book-cover {
    padding: 1rem;
  }

  .book-title {
    font-size: 1rem;
    -webkit-line-clamp: 3; /* 在小屏幕上减少标题行数 */
  }

  .book-meta {
    font-size: 0.75rem;
    gap: 0.25rem;
  }

  .book-status {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }

  .book-header {
    gap: 0.3rem;
  }

  .book-decoration {
    width: 40px;
    height: 40px;
    bottom: 0.75rem;
    right: 0.75rem;
  }
}
