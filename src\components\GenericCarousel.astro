---
// 通用轮播组件，可以用于显示不同类型的内容
interface Props {
  id: string; // 轮播的唯一ID
  items: unknown[]; // 轮播项目数组
  autoplay?: boolean; // 是否自动轮播
  autoplaySpeed?: number; // 自动轮播间隔时间(毫秒)
}

const { id, items, autoplay = true, autoplaySpeed = 5000 } = Astro.props;
---

{items.length > 0 && (
  <div class="generic-carousel-container" id={id}>
    <div class="carousel-wrapper">
      <button class="carousel-nav prev-button" aria-label="上一个">
        <span class="i-mdi-chevron-left"></span>
      </button>

      <div class="carousel-track">
        {items.map((_, index) => (
          <div class="carousel-item" data-index={index}>
            <slot name={`item-${index}`} />
          </div>
        ))}
      </div>

      <button class="carousel-nav next-button" aria-label="下一个">
        <span class="i-mdi-chevron-right"></span>
      </button>
    </div>

    <div class="carousel-indicators">
      {items.map((_, index) => (
        <button
          class={`indicator-dot ${index === 0 ? 'active' : ''}`}
          data-index={index}
          aria-label={`切换到第${index + 1}个项目`}
        ></button>
      ))}
    </div>
  </div>
)}

<script is:inline define:vars={{ id, autoplay, autoplaySpeed }}>
  // 初始化轮播功能
  function initCarousel(carouselId) {
    const container = document.getElementById(carouselId);
    if (!container) return;

    const carouselTrack = container.querySelector('.carousel-track');
    const carouselItems = container.querySelectorAll('.carousel-item');
    const prevButton = container.querySelector('.prev-button');
    const nextButton = container.querySelector('.next-button');
    const indicators = container.querySelectorAll('.indicator-dot');

    if (!carouselTrack || !carouselItems.length || !prevButton || !nextButton) return;

    let currentIndex = 0;
    const itemCount = carouselItems.length;

    // 设置初始状态
    updateCarousel();

    // 上一个按钮点击事件
    prevButton.addEventListener('click', () => {
      currentIndex = (currentIndex - 1 + itemCount) % itemCount;
      updateCarousel();
    });

    // 下一个按钮点击事件
    nextButton.addEventListener('click', () => {
      currentIndex = (currentIndex + 1) % itemCount;
      updateCarousel();
    });

    // 指示器点击事件
    indicators.forEach(dot => {
      dot.addEventListener('click', () => {
        const index = parseInt(dot.getAttribute('data-index') || '0', 10);
        currentIndex = index;
        updateCarousel();
      });
    });

    // 触摸滑动支持
    let touchStartX = 0;
    let touchEndX = 0;

    carouselTrack.addEventListener('touchstart', (e) => {
      const touchEvent = e;
      touchStartX = touchEvent.changedTouches[0].screenX;
    }, { passive: true });

    carouselTrack.addEventListener('touchend', (e) => {
      const touchEvent = e;
      touchEndX = touchEvent.changedTouches[0].screenX;
      handleSwipe();
    }, { passive: true });

    function handleSwipe() {
      const swipeThreshold = 50; // 最小滑动距离
      if (touchEndX < touchStartX - swipeThreshold) {
        // 向左滑动，显示下一个
        currentIndex = (currentIndex + 1) % itemCount;
        updateCarousel();
      } else if (touchEndX > touchStartX + swipeThreshold) {
        // 向右滑动，显示上一个
        currentIndex = (currentIndex - 1 + itemCount) % itemCount;
        updateCarousel();
      }
    }

    // 自动轮播
    let autoplayInterval;
    const startAutoplay = () => {
      if (!autoplay) return;
      autoplayInterval = window.setInterval(() => {
        currentIndex = (currentIndex + 1) % itemCount;
        updateCarousel();
      }, autoplaySpeed); // 自定义切换间隔
    };

    const stopAutoplay = () => {
      if (autoplayInterval) {
        window.clearInterval(autoplayInterval);
      }
    };

    // 鼠标悬停时暂停自动轮播
    carouselTrack.addEventListener('mouseenter', stopAutoplay);
    carouselTrack.addEventListener('mouseleave', startAutoplay);

    // 触摸时暂停自动轮播
    carouselTrack.addEventListener('touchstart', stopAutoplay, { passive: true });
    carouselTrack.addEventListener('touchend', startAutoplay, { passive: true });

    // 更新轮播状态
    function updateCarousel() {
      // 更新轮播项位置
      carouselItems.forEach((item, index) => {
        const offset = index - currentIndex;
        item.style.transform = `translateX(${offset * 100}%)`;
        item.setAttribute('aria-hidden', String(index !== currentIndex));
      });

      // 更新指示器状态
      indicators.forEach((dot, index) => {
        dot.classList.toggle('active', index === currentIndex);
      });
    }

    // 开始自动轮播
    startAutoplay();
  }

  // 页面加载完成后初始化轮播
  document.addEventListener('DOMContentLoaded', () => initCarousel(id));
</script>

<style>
  /* 这里只包含基本样式，详细样式在carousel.css中 */
  .generic-carousel-container {
    position: relative;
    margin-bottom: 2rem;
  }
</style>
