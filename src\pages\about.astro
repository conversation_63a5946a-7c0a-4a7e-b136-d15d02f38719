---
import { getEntry, render } from 'astro:content'
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import AboutComments from '~/components/AboutComments.astro'
import SiteSeo from '~/components/SiteSeo.astro'
import { themeConfig } from '~/.config'

const aboutPost = await getEntry('spec', 'about')
if (!aboutPost) {
  throw new Error('No about post found')
}
const { Content } = await render(aboutPost)

// 获取社交媒体链接
const socialLinks = themeConfig.site.socialLinks.map(link => link.href);
---

<LayoutDefault>
  <SiteSeo slot="seo" title={aboutPost.data.title} desc="关于我的个人介绍" />

  <!-- 关于页结构化数据 -->
  <script is:inline type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "Person",
    "name": themeConfig.site.author,
    "description": "关于我的个人介绍",
    "url": new URL("about/", Astro.site).toString(),
    "sameAs": socialLinks
  })} />

  <div class="about-container tablet-centered">
    <div class="about-header">
      <h1 class="page-title">{aboutPost.data.title}</h1>
    </div>

    <div class="about-card content-card">
      <article class="about-content card-content prose">
        <Content />
      </article>
    </div>

    <div class="about-comments-section content-card">
      <AboutComments />
    </div>
  </div>
</LayoutDefault>

<style>
  .about-container {
    max-width: 100%;
    animation: fadeIn 0.8s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .about-header {
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
  }

  .about-meta {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
    color: #666;
    font-size: 0.95rem;
  }

  .dark .about-meta {
    color: #aaa;
  }

  .about-date {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .about-date-icon {
    font-size: 1.1rem;
  }

  /* 关于页卡片样式已移至card-responsive.css */
  .about-content {
    line-height: 1.8;
  }

  .about-comments-section {
    margin-top: 1.5rem;
  }
</style>
