---
title: 图片优化指南
---

# 图片优化指南

川流主题内置了强大的图片优化功能，可以自动处理图片，提高网站性能和用户体验。本指南将介绍如何使用这些功能。

## 优化功能概述

川流主题的图片优化系统提供以下功能：

1. **自动WebP转换**：将图片转换为现代的WebP格式，大幅减小文件体积
2. **响应式图片**：自动生成多种尺寸的图片，适应不同设备和屏幕
3. **懒加载**：图片只在进入视口时才加载，提高页面初始加载速度
4. **模糊占位符**：在图片加载前显示低质量的模糊预览
5. **构建时优化**：在构建网站时自动压缩和优化静态图片

## 配置选项

图片优化功能可以在 `.config/user.ts` 文件中配置：

```typescript
// .config/user.ts
export default {
  // 其他配置...
  
  // 图片优化配置
  imageOptimization: {
    enable: true,                // 是否启用图片优化
    quality: 80,                 // 图片质量(1-100)
    formats: ['webp', 'jpeg'],   // 生成的图片格式
    widths: [640, 750, 828, 1080, 1200, 1920], // 响应式图片宽度
    usePlaceholder: true,        // 是否使用模糊占位符
    placeholderSize: 60,         // 占位符图片宽度
    cdn: {
      enable: false,             // 是否启用CDN
      provider: '',              // CDN提供商，支持'cloudinary'、'imgix'等
      domain: '',                // CDN域名
      options: {}                // CDN特定选项
    }
  }
}
```

## 在Markdown中使用

在Markdown文件中，您可以像往常一样使用图片语法，系统会自动应用优化：

```markdown
![图片描述](./images/example.jpg)
```

系统会自动：
1. 将图片转换为WebP格式（同时保留原格式作为后备）
2. 生成多种尺寸的响应式版本
3. 添加懒加载属性
4. 添加模糊占位符

## 在组件中使用

如果您需要在Astro组件中使用优化图片，可以直接导入`OptimizedImage`组件：

```astro
---
import OptimizedImage from '../components/OptimizedImage.astro';
import myImage from '../assets/my-image.jpg';
---

<OptimizedImage 
  src={myImage} 
  alt="我的图片描述" 
  widths={[400, 800, 1200]}
  sizes="(max-width: 800px) 100vw, 800px"
/>
```

## 手动优化静态图片

您可以使用内置的图片优化脚本来处理静态图片：

```bash
npm run optimize-images
```

这将处理 `public/images` 和 `src/assets` 目录中的图片，并将优化后的版本保存到 `public/optimized-images` 目录。

## 最佳实践

1. **使用适当的图片尺寸**：上传的图片尺寸不应过大，建议宽度不超过2000px
2. **提供有意义的alt文本**：为所有图片添加描述性的alt文本，有利于SEO和无障碍性
3. **使用描述性的文件名**：图片文件名应该包含关键词，如`astro-framework-logo.jpg`而非`img001.jpg`
4. **选择合适的图片格式**：
   - 照片和复杂图像：JPEG或WebP
   - 需要透明度的图像：PNG或WebP
   - 简单图形和图标：SVG
5. **预先压缩图片**：虽然系统会自动压缩图片，但预先使用工具如TinyPNG压缩可以获得更好的效果

## 性能影响

启用图片优化功能后，您可以期待以下性能改进：

- 页面加载速度提高30-50%
- 图片加载时间减少40-60%
- 总页面大小减少20-40%
- 更好的移动设备体验
- 更高的Google Lighthouse性能分数

## 故障排除

如果您遇到图片优化相关的问题，请尝试以下解决方案：

1. **图片不显示**：
   - 检查图片路径是否正确
   - 确认图片文件存在
   - 查看浏览器控制台是否有错误

2. **图片优化不生效**：
   - 确认在配置中启用了图片优化功能
   - 检查图片格式是否受支持（JPG、PNG、GIF）
   - 尝试清除缓存并重新构建

3. **构建时间过长**：
   - 减少需要处理的图片数量
   - 调整响应式尺寸配置，减少生成的图片版本
   - 使用`build:no-optimize`命令跳过图片优化

如果问题仍然存在，请查看控制台输出或提交GitHub issue获取帮助。
