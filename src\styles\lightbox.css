/**
 * 灯箱样式
 */

.lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lightbox.active {
  display: flex;
  opacity: 1;
}

.lightbox-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
}

.lightbox-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.lightbox-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  transition: transform 0.3s ease-out;
  transform-origin: center center;
}

.lightbox-image {
  display: block;
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  cursor: grab;
}

.lightbox-image:active {
  cursor: grabbing;
}

.lightbox-close,
.lightbox-prev,
.lightbox-next {
  position: absolute;
  background: transparent;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  z-index: 2;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.3s ease;
  border-radius: 50%;
}

.lightbox-close:hover,
.lightbox-prev:hover,
.lightbox-next:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.lightbox-close {
  top: 20px;
  right: 20px;
}

.lightbox-prev {
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.lightbox-next {
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.lightbox-caption {
  position: absolute;
  bottom: 80px;
  left: 0;
  width: 100%;
  text-align: center;
  color: white;
  padding: 10px;
  z-index: 2;
  font-size: 16px;
  background-color: rgba(0, 0, 0, 0.5);
}

.lightbox-counter {
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  z-index: 2;
  font-size: 14px;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 15px;
}

.lightbox-toolbar {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 2;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 20px;
}

.lightbox-toolbar button {
  background: transparent;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.lightbox-toolbar button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动设备适配 */
@media (max-width: 768px) {
  .lightbox-prev,
  .lightbox-next {
    width: 40px;
    height: 40px;
  }
  
  .lightbox-close {
    top: 10px;
    right: 10px;
  }
  
  .lightbox-caption {
    bottom: 60px;
    font-size: 14px;
  }
  
  .lightbox-counter {
    top: 10px;
    left: 10px;
    font-size: 12px;
  }
  
  .lightbox-toolbar {
    bottom: 10px;
  }
}

/* 添加一些动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.lightbox.active .lightbox-overlay,
.lightbox.active .lightbox-content {
  animation: fadeIn 0.3s ease forwards;
}

/* 为图片添加灯箱触发样式 */
img.lightbox-trigger {
  cursor: zoom-in;
}

/* 当图片可点击时添加微小的悬停效果 */
img.lightbox-trigger:hover {
  opacity: 0.95;
  transform: scale(1.01);
  transition: all 0.2s ease;
}
