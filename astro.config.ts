/**
 * Astro配置文件
 *
 * 该文件定义了Astro项目的所有配置选项，包括站点URL、构建选项、
 * Markdown处理、集成插件等。修改此文件可以自定义网站的各种行为。
 */

// 导入官方集成
import mdx from '@astrojs/mdx' // MDX支持，允许在Markdown中使用JSX
import sitemap from '@astrojs/sitemap' // 自动生成站点地图
import robotsTxt from 'astro-robots-txt' // 自动生成robots.txt
import { defineConfig } from 'astro/config' // Astro配置函数

// 导入Markdown处理插件
import rehypeKatex from 'rehype-katex' // 数学公式渲染支持
import remarkMath from 'remark-math' // Markdown中的数学语法支持

// 导入CSS框架
import UnoCSS from 'unocss/astro' // 原子化CSS框架

// 导入项目配置和自定义插件
import { themeConfig } from './src/.config' // 主题配置
import { remarkOptimizedImages } from './src/utils/optimizedImages' // 图片优化插件

// https://astro.build/config
export default defineConfig({
  // === 基本站点配置 ===//

  // 网站URL - 用于生成规范链接和站点地图
  site: themeConfig.site.website,

  // 预取功能 - 提高页面导航速度，预加载链接目标
  prefetch: true,

  // 基础路径 - 如果网站部署在子目录，例如example.com/blog/，则设置为'/blog/'
  base: '/',
  // === 构建配置 ===//

  // Vite构建选项 - 控制底层构建工具的行为
  vite: {
    build: {
      // CSS压缩工具 - 使用LightningCSS替代默认的esbuild
      // LightningCSS提供更好的压缩率和更现代的CSS特性支持
      cssMinify: 'lightningcss',
    },
  },

  // === Markdown配置 ===//

  markdown: {
    // Remark插件 - 处理Markdown语法和结构
    remarkPlugins: [
      remarkMath, // 支持数学公式语法
      remarkOptimizedImages, // 自动优化Markdown中的图片
    ],

    // Rehype插件 - 处理HTML输出
    rehypePlugins: [
      rehypeKatex, // 渲染数学公式为HTML
    ],

    // 代码高亮配置
    shikiConfig: {
      theme: 'dracula', // 代码块主题
      wrap: true, // 启用代码换行
    },
  },

  // === 集成插件 ===//

  integrations: [
    // UnoCSS - 高性能且灵活的原子化CSS引擎
    UnoCSS({ injectReset: true }), // 注入CSS重置样式

    // MDX支持 - 允许在Markdown中使用组件
    mdx(),

    // Robots.txt生成器 - 控制搜索引擎爬虫行为
    robotsTxt(),

    // 站点地图生成器 - 帮助搜索引擎发现和索引网站内容
    sitemap({
      // 自定义站点地图配置
      changefreq: 'weekly', // 页面更新频率
      priority: 0.7, // 页面优先级(0.0-1.0)
      lastmod: new Date(), // 最后修改日期
      filter: page => !page.includes('/drafts/'), // 排除草稿页面
    }),
  ],
})
