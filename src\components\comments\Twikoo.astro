---
import { themeConfig } from '~/.config'

interface Props {
  path: string
}

const twikooConfig = {
  ...themeConfig.comment?.twikoo,
  el: '#tcomment',
  path: Astro.props.path,
}
---

<div id="tcomment"></div>
<script is:inline src="https://cdn.jsdelivr.net/npm/twikoo@1.6.44/dist/twikoo.all.min.js"></script>
<script is:inline define:vars={{ config: twikooConfig }} defer>
  // 使用立即执行函数确保作用域隔离
  ;(function () {
    // 只在DOMContentLoaded事件时初始化
    document.addEventListener('DOMContentLoaded', () => {
      const tcommentDiv = document.getElementById('tcomment')
      if (typeof twikoo !== 'undefined' && tcommentDiv) {
        twikoo.init(config)
      }
    })
  })()
</script>

<style is:global>
  .tk-meta-input {
    background-color: white;
  }
</style>
