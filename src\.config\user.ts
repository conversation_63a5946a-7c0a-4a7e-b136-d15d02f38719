import type { UserConfig } from '~/types'

export const userConfig: Partial<UserConfig> = {
  // 站点基本信息
  site: {
    title: '盲流',
    subtitle: 'Wandering Souls',
    author: '盲流',
    description: '盲流,自由,信念,理想,Flow, freedom, belief, ideal,Wandering Souls,Hidden Tunes',
    // 请将此处替换为您的实际网站域名，例如 'https://chuanliu.com'
    // 确保使用HTTPS协议，并决定是否包含www前缀
    website: 'https://mangliu.net/',
    pageSize: 5,
    socialLinks: [
      {
        name: 'github',
        href: 'https://github.com/tomato10010',
      },
      {
        name: 'twitter',
        href: 'https://x.com/tomatotvtv',
      },
      {
        name: 'telegram',
        href: 'https://t.me/cnpotato',
      },
      {
        name: 'email',
        href: 'mailto:<EMAIL>',
      },
      {
        name: 'youtube',
        href: 'https://youtube.com/@tomatotop',
      },
      {
        name: 'wechat',
        href: 'https://mp.weixin.qq.com/your-article',
        title: '微信公众号',
      },
      {
        name: 'rss',
        href: '/atom.xml',
      },
    ],
    navLinks: [
      {
        name: '首页',
        href: '/',
        icon: 'home',
      },
      {
        name: '归档',
        href: '/posts',
        icon: 'archive-outline',
      },
      {
        name: '分类',
        href: '/categories',
        icon: 'folder-outline',
      },
      {
        name: '标签',
        href: '/tags',
        icon: 'tag-outline',
      },
      {
        name: '系列',
        href: '/series',
        icon: 'book-open-outline',
      },
      {
        name: '友链',
        href: '/links',
        icon: 'link-variant',
      },
      {
        name: '留言板',
        href: '/guestbook',
        icon: 'message-outline',
      },
      {
        name: '搜索',
        href: '/search',
        icon: 'magnify',
      },
      {
        name: '关于',
        href: '/about',
        icon: 'information-outline',
      },
    ],
    footer: [
      '© %year <a target="_blank" href="%website">%author</a>',
      '一切为了自由',
    ],
    // 分类映射表，用于将中文分类名转换为英文URL
    categoryMap: [
      { name: '技术', path: 'technology' },
      { name: '生活', path: 'life' },
      { name: '思考', path: 'thoughts' },
      { name: '阅读', path: 'reading' },
      { name: '博客', path: 'blog' },
      // 可以根据需要添加更多映射
    ],
    // 标签映射表，用于将中文标签名转换为英文URL
    tagMap: [
      { name: '编程', path: 'programming' },
      { name: '旅行', path: 'travel' },
      { name: '阅读', path: 'reading' },
      { name: '笔记', path: 'notes' },
      { name: '教程', path: 'tutorial' },
      { name: '欢迎', path: 'welcome' },
      { name: '入门', path: 'getting-started' },
      { name: 'Astro', path: 'astro' },
      // 可以根据需要添加更多映射
    ],
    // 系列映射表，用于将中文系列名转换为英文URL
    seriesMap: [
      { name: 'Astro系列教程', path: 'astro-tutorial-series' },
      { name: '自定义Slug系列测试', path: 'custom-slug-series-test' },
      // 可以根据需要添加更多映射
    ],
  },
  // 外观设置
  appearance: {
    theme: 'light', // 'light' | 'dark' | 'system'
    locale: 'zh-cn',
  },
  // 评论系统设置
  comment: {
    twikoo: {
      envId: 'https://chuanliutwikoo.netlify.app/.netlify/functions/twikoo', // 您的Twikoo环境ID
      path: 'window.location.pathname',
    },
  },
  // 统计分析设置
  analytics: {
    googleAnalyticsId: '', // Google Analytics ID
    umamiAnalyticsId: '',
  },
  // 赞赏功能设置
  donate: {
    enable: true,
    paypal: 'https://www.paypal.me/yourusername', // 替换为您的PayPal链接
    crypto: {
      btc: 'your-btc-address', // 替换为您的比特币地址
      eth: 'your-eth-address', // 替换为您的以太坊地址
      usdt: 'your-usdt-address', // 替换为您的USDT地址
    },
  },

  // 相关文章设置
  relatedPosts: {
    enable: true, // 是否启用相关文章功能
    maxPosts: 3, // 最多显示的相关文章数量
    randomize: true, // 是否随机显示相关文章
  },

  // 首页系列文章轮播设置
  carousel: {
    enable: false, // 是否启用轮播
    maxItems: 5, // 最多显示几个轮播项
    showArticles: 3, // 每个系列显示前几篇文章
    sortBy: 'custom', // 排序方式：'latest'(最新更新)、'articleCount'(文章数量)或'custom'(自定义)
    autoplay: true, // 是否自动轮播
    autoplaySpeed: 3000, // 自动轮播间隔时间(毫秒)
    // 自定义系列列表，如果设置了此项，将只显示这些系列
    customSeries: ['Astro系列教程', '川流主题使用教程', '自定义Slug系列测试'],
    // 自定义排序方式，仅当sortBy为'custom'时生效
    customSort: 'alphabetical', // 可选值：'asc'(升序)、'desc'(降序)、'alphabetical'(字母顺序)、'random'(随机)
    // 设备显示控制，可以控制在不同设备上是否显示轮播
    deviceDisplay: {
      desktop: true, // 桌面端设置
      tablet: true, // 平板端设置
      mobile: true, // 移动端设置
    },
  },

  // AdSense配置
  adsense: {
    enable: false, // 设置为true启用AdSense
    clientId: 'ca-pub-XXXXXXXXXXXXXXXX', // 替换为您的AdSense发布商ID
  },

  // 图片灯箱配置
  lightbox: {
    enable: true, // 是否启用灯箱功能
    animationSpeed: 300, // 动画速度(毫秒)
    bgOpacity: 0.9, // 背景不透明度(0-1)
    zoomStep: 0.25, // 每次缩放步长
    maxZoom: 3, // 最大缩放倍数
    minZoom: 0.5, // 最小缩放倍数
  },

  // 图片优化配置
  imageOptimization: {
    enable: true, // 是否启用图片优化
    quality: 85, // 图片质量提高到85(1-100)
    formats: ['webp', 'avif'], // 添加AVIF格式支持，移除JPEG
    widths: [480, 640, 768, 1024, 1366, 1600, 1920], // 自定义响应式宽度
    usePlaceholder: true, // 保持使用模糊占位符
    placeholderSize: 40, // 减小占位符尺寸以加快加载
    cdn: {
      enable: false, // 暂不启用CDN
      provider: '', // CDN提供商
      domain: '', // CDN域名
      options: {}, // CDN特定选项
    },
  },

}
