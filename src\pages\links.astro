---
import LayoutDefault from '~/layouts/LayoutDefault.astro';
import SiteSeo from '~/components/SiteSeo.astro';
import FriendLinks from '~/components/FriendLinks.astro';

const { translate: t } = Astro.locals;
const title = t('Links') || '友情链接';
const description = "我的朋友们和他们的博客";

// 获取友链数据 - 从FriendLinks组件中导入
import { friendLinks } from '~/components/FriendLinks.astro';

// 为结构化数据准备友链列表项
const itemListElements = friendLinks.map((link, index) => ({
  "@type": "ListItem",
  "position": index + 1,
  "item": {
    "@type": "Thing",
    "name": link.name,
    "url": link.url,
    "description": link.description
  }
}));
---

<LayoutDefault>
  <SiteSeo slot="seo" title={title} desc={description} />

  <!-- 友链页结构化数据 -->
  <script is:inline type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": title,
    "description": description,
    "url": new URL("links/", Astro.site).toString(),
    "itemListElement": itemListElements
  })} />

  <FriendLinks />
</LayoutDefault>
