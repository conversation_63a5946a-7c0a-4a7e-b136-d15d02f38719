---
import { themeConfig } from '~/.config'
import { getPathFromTag } from '~/utils'

interface Props {
  tag: string;
}

const { tag } = Astro.props;
const tagPath = getPathFromTag(tag, themeConfig.site.tagMap);
---

<a href={`/tags/${tagPath}`} class="post-tag">
  <span class="i-mdi-tag-outline tag-icon"></span>
  <span class="tag-text">{tag}</span>
</a>

<style>
  .post-tag {
    display: inline-flex;
    align-items: center;
    margin-right: 0.5rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    background-color: rgba(46, 64, 91, 0.08);
    color: var(--tw-color-primary, #2e405b);
    font-size: 0.85rem;
    text-decoration: none;
    transition: all 0.2s ease;
  }

  .post-tag:hover {
    background-color: rgba(46, 64, 91, 0.15);
    text-decoration: none;
  }

  .tag-icon {
    margin-right: 0.25rem;
    width: 0.9rem;
    height: 0.9rem;
  }

  .tag-text {
    line-height: 1;
  }

  /* 暗黑模式 */
  .dark .post-tag {
    background-color: rgba(255, 255, 255, 0.1);
    color: #e5e7eb;
  }

  .dark .post-tag:hover {
    background-color: rgba(255, 255, 255, 0.15);
  }
</style>
