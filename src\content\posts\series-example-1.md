---
title: "Astro系列教程（一）：入门基础"
pubDate: 2023-12-01
categories: ["技术", "教程"]
tags: ["Astro", "前端", "静态站点"]
description: "Astro系列教程的第一篇，介绍Astro的基本概念、安装和项目结构。"
series:
  name: "Astro系列教程"
  order: 1
---

# Astro系列教程（一）：入门基础

欢迎来到Astro系列教程的第一篇文章！在这个系列中，我们将从零开始学习如何使用Astro构建高性能的静态网站。

## 什么是Astro？

[Astro](https://astro.build/)是一个现代的静态站点生成器，专为内容驱动的网站设计。它的主要特点包括：

- **零JavaScript默认**：只在需要时发送JavaScript
- **基于组件**：使用任何UI框架（React、Vue、Svelte等）
- **服务器优先**：将繁重的渲染工作移至构建时
- **极速性能**：几乎100%的Lighthouse得分
- **丰富的生态**：集成了众多流行工具和服务

## 安装Astro

开始使用Astro非常简单，只需要几个命令：

```bash
# 创建新项目
npm create astro@latest my-astro-project

# 进入项目目录
cd my-astro-project

# 启动开发服务器
npm run dev
```

## Astro项目结构

一个典型的Astro项目结构如下：

```
my-astro-project/
├── public/          # 静态资源目录
├── src/
│   ├── components/  # UI组件
│   ├── content/     # 内容集合（博客文章等）
│   ├── layouts/     # 页面布局
│   └── pages/       # 页面路由
├── astro.config.mjs # Astro配置文件
└── package.json     # 项目依赖
```

### pages目录

`src/pages`目录是Astro的基于文件的路由系统的核心。每个`.astro`、`.md`或`.mdx`文件都会自动成为网站的一个页面：

- `src/pages/index.astro` → `yourdomain.com/`
- `src/pages/about.astro` → `yourdomain.com/about`
- `src/pages/posts/first-post.md` → `yourdomain.com/posts/first-post`

### components目录

`src/components`目录用于存放可复用的UI组件。Astro组件使用类似HTML的语法，但也支持JavaScript表达式和组件导入：

```astro
---
// src/components/Button.astro
const { text } = Astro.props;
---

<button class="btn">{text}</button>

<style>
  .btn {
    padding: 0.5rem 1rem;
    background-color: #4c1d95;
    color: white;
    border-radius: 0.25rem;
  }
</style>
```

## 在下一篇文章中...

在本系列的下一篇文章中，我们将深入探讨Astro的内容集合系统，学习如何创建和管理博客文章、分类和标签。

敬请期待[Astro系列教程（二）：内容集合系统](/posts/series-example-2)！
