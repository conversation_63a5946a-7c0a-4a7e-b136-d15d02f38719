/*
 * 文章正文页紧凑样式
 * 缩小各个模块的上下间距，使页面看起来更紧凑
 */

/* 1. 文章正文区域间距优化 */
.prose {
  /* 减小段落间距 */
  & p {
    margin-bottom: 0.75rem;
  }

  /* 减小标题下方间距 */
  & h1,
  & h2,
  & h3,
  & h4,
  & h5,
  & h6 {
    margin-bottom: 0.75rem;
  }

  /* 减小列表间距 */
  & ul,
  & ol {
    margin-top: 0.5rem;
    margin-bottom: 0.75rem;
  }

  /* 减小列表项间距 */
  & li {
    margin-bottom: 0.25rem;
  }

  /* 减小代码块间距 */
  & pre {
    margin: 0.75rem 0;
  }

  /* 减小引用块间距 */
  & blockquote {
    margin: 0.75rem 0;
    padding: 0.5rem 1rem;
  }
}

/* 2. 系列导航模块间距优化 */
.series-navigation {
  margin: 1.25rem 0;
  padding: 0.75rem;
}

.series-list {
  margin: 0.5rem 0;
  gap: 0.25rem;
}

/* 3. 赞赏按钮间距优化 */
.donate-container {
  margin: 0.75rem 0 0.25rem;
}

/* 4. 社交分享模块间距优化 */
.social-share {
  margin: 0.25rem 0 0;
  padding: 0.25rem 0 0;
  border-bottom: none;
  border-top-width: 1px;
  margin-bottom: 0;
}

.share-title {
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

/* 5. 相关文章模块间距优化 */
.related-posts {
  margin-top: 0;
  padding: 0.5rem;
  border-radius: 0.25rem;
  border-width: 1px;
}

.related-posts h3 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  padding-bottom: 0.125rem;
}

.related-posts li {
  margin-bottom: 0.125rem;
}

/* 6. 文章导航模块间距优化 */
.post-navigation {
  margin: 0 auto 0;
  padding-top: 0;
  border-top: none;
}

.no-margin {
  margin: 0 !important;
  padding: 0 !important;
}

/* 7. 评论区间距优化 */
.comments-container {
  margin-top: 1.25rem;
}

.comments-title {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
}

/* 8. 整体文章容器间距优化 */
article.prose {
  margin-bottom: 1.25rem;
}

/* 9. 移动端优化 */
@media (max-width: 768px) {
  .series-navigation {
    margin: 0.75rem 0;
    padding: 0.5rem;
  }

  .donate-container {
    margin: 0.5rem 0 0.25rem;
  }

  .social-share {
    margin: 0.25rem 0 0;
    padding: 0.25rem 0 0;
  }

  .share-button {
    width: 2.25rem;
    height: 2.25rem;
  }

  .share-button span {
    font-size: 1.1rem;
  }

  .post-navigation {
    margin: 0 auto 0;
    padding-top: 0;
    border-top: none;
  }

  .related-posts {
    margin-top: 0;
    padding: 0.375rem;
    border-width: 1px;
  }
}

/**
 * 文章列表间距修复
 * 优化首页和其他页面的文章列表间距
 */

/* 首页文章列表容器 */
.post-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem; /* 增加文章之间的间距 */
  margin-bottom: 1.5rem; /* 增加列表底部间距 */
}

/* 文章容器样式 */
.post-list > article.prose {
  padding-bottom: 1.5rem; /* 增加文章底部内边距 */
  margin-bottom: 0; /* 移除默认底部外边距，使用gap控制间距 */
}

/* 动态加载的文章容器样式 */
.article-container {
  margin-bottom: 1.5rem; /* 增加动态加载文章的间距 */
  padding-bottom: 1.5rem; /* 增加底部内边距 */
}

/* 移动端优化 */
@media (max-width: 768px) {
  .post-list {
    gap: 1.5rem; /* 移动端稍微减小间距 */
  }

  .post-list > article.prose,
  .article-container {
    padding-bottom: 1.25rem; /* 减小内边距 */
  }
}
