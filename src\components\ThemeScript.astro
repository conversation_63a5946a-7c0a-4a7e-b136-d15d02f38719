---
import { themeConfig } from "~/.config";

const theme = themeConfig.appearance.theme;
---

<script is:inline define:vars={{ theme }}>
  function updateTheme(theme) {
    // Check if theme is system
    if (theme === "system") {
      // Check OS preference
      const prefersDark = window.matchMedia(
        "(prefers-color-scheme: dark)"
      ).matches;
      document.documentElement.classList.toggle("dark", prefersDark);
    } else {
      // Set theme directly
      document.documentElement.classList.toggle("dark", theme === "dark");
    }
  }

  // Initial theme setup
  updateTheme(theme);

  // Listen for OS theme changes
  window
    .matchMedia("(prefers-color-scheme: dark)")
    .addEventListener("change", (e) => {
      if (theme === "system") {
        document.documentElement.classList.toggle("dark", e.matches);
      }
    });
</script>
