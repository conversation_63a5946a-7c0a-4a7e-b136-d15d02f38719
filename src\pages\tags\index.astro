---
import { themeConfig } from '~/.config'
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import SiteSeo from '~/components/SiteSeo.astro'
import { getTags, getPathFromTag } from '~/utils/index'

const { translate: t } = Astro.locals
const tagMap = await getTags()

const list = getListFromMap(tagMap)

function getListFromMap(map: typeof tagMap) {
  return Array.from(map).map(([key, value]) => ({
    title: key,
    href: `/tags/${getPathFromTag(key, themeConfig.site.tagMap)}`,
    size: t('tag_count', value.length),
    count: value.length,
  }))
}

// 按文章数量排序
list.sort((a, b) => b.count - a.count)

const tagsTitle = t('tags_title') || '标签云'
const tagsDescription = t('tags_description') || '查看所有文章标签'
---

<LayoutDefault>
  <SiteSeo slot="seo" title={tagsTitle} desc={tagsDescription} />

  <div class="tags-container">
    <h1 class="page-title">{tagsTitle}</h1>
    <p class="page-description">{tagsDescription}</p>

    <div class="tag-cloud">
      {
        list.map((tag) => (
          <a
            href={tag.href}
            class="tag-item not-underline-hover"
            style={`--tag-size: ${Math.max(0.8, Math.min(2, 0.8 + tag.count * 0.1))}em;`}
          >
            <span class="tag-name">{tag.title}</span>
            <span class="tag-count">{tag.size}</span>
          </a>
        ))
      }
    </div>
  </div>
</LayoutDefault>

<style>
  .tags-container {
    max-width: 100%;
    margin: 0 auto;
  }

  /* 使用全局 .page-title 样式，移除自定义样式 */
  .tags-title {
    margin-bottom: 0.5rem;
  }

  .tags-description {
    color: #666;
    margin-bottom: 2rem;
    text-align: center;
  }

  .dark .tags-description {
    color: #aaa;
  }

  .tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    padding: 1rem 0;
  }

  .tag-item {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 30px;
    background-color: rgba(46, 64, 91, 0.08);
    color: var(--tw-color-primary, #2e405b);
    font-size: var(--tag-size, 1em);
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .tag-item:hover {
    background-color: rgba(46, 64, 91, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    text-decoration: none;
  }

  .tag-name {
    margin-right: 0.5rem;
  }

  .tag-count {
    font-size: 0.8em;
    opacity: 0.7;
  }

  /* 暗黑模式 */
  .dark .tag-item {
    background-color: rgba(255, 255, 255, 0.1);
    color: #e5e7eb;
  }

  .dark .tag-item:hover {
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .tag-cloud {
      gap: 0.75rem;
    }

    .tag-item {
      padding: 0.4rem 0.8rem;
    }
  }
</style>
