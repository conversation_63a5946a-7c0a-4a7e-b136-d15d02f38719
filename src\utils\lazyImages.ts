import { visit } from 'unist-util-visit'
import type { Root } from 'mdast'

/**
 * 为 Markdown 内容中的图片添加懒加载属性的 remark 插件
 */
export function remarkLazyImages() {
  return (tree: Root) => {
    // 首先检查 tree 是否有效
    if (!tree || typeof tree !== 'object') {
      return
    }

    try {
      // 访问所有图片节点
      visit(tree, 'image', (node: any) => {
        // 确保 node.data 存在
        node.data = node.data || {}
        
        // 确保 node.data.hProperties 存在
        node.data.hProperties = node.data.hProperties || {}
        
        // 添加 loading="lazy" 属性
        node.data.hProperties.loading = 'lazy'
      })
    } catch (error) {
      console.error('Error in remarkLazyImages plugin:', error)
    }
  }
}
