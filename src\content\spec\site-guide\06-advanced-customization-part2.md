---
title: "6. 高级自定义（续）"
pubDate: 2023-12-31
---

# 6. 高级自定义（续）

## 样式覆盖技巧

川流主题使用 UnoCSS 进行样式管理，这是一个原子化 CSS 框架。您可以通过多种方式自定义样式。

### 使用 UnoCSS 原子类

最简单的样式自定义方法是使用 UnoCSS 原子类。川流主题已经配置了 UnoCSS，您可以直接在 HTML 中使用原子类：

```html
<div class="text-blue-500 font-bold text-xl p-4 m-2 rounded-lg shadow-md">
  使用原子类样式的元素
</div>
```

### 修改 UnoCSS 配置

如果您想添加自定义原子类或修改现有类，可以编辑 `uno.config.js` 文件：

```javascript
// uno.config.js
export default defineConfig({
  // 添加自定义规则
  rules: [
    [
      /^custom-gradient-(.+)-(.+)$/,
      ([, color1, color2]) => ({
        background: `linear-gradient(to right, ${color1}, ${color2})`,
      }),
    ],
  ],
  // 添加快捷方式
  shortcuts: [
    ['card', 'p-4 bg-white rounded-lg shadow-md'],
    ['btn', 'px-4 py-2 rounded bg-primary text-white hover:opacity-80'],
  ],
  // 自定义主题
  theme: {
    colors: {
      custom: '#ff5500',
    },
  },
});
```

### 添加全局 CSS

您可以创建自定义 CSS 文件来覆盖主题样式：

1. 创建 `src/styles/custom.css` 文件
2. 添加您的自定义样式
3. 在 `src/layouts/LayoutDefault.astro` 中导入

```css
/* src/styles/custom.css */
:root {
  --custom-color: #ff5500;
}

.prose h1 {
  color: var(--custom-color);
}

.prose p {
  line-height: 1.8;
}

/* 覆盖主题组件样式 */
.post-title {
  font-family: 'Your Custom Font', sans-serif;
}
```

然后在布局中导入：

```astro
---
// src/layouts/LayoutDefault.astro
import '../styles/custom.css';
---
```

### 使用 style 标签

对于特定页面的样式，您可以直接在 Astro 组件中使用 `<style>` 标签：

```astro
---
// 页面或组件
---

<div class="custom-element">
  自定义样式的元素
</div>

<style>
  .custom-element {
    color: purple;
    border: 2px dashed gold;
    padding: 1rem;
  }
</style>
```

### 使用 CSS 变量

川流主题使用 CSS 变量定义颜色和其他样式属性。您可以覆盖这些变量来自定义主题外观：

```css
:root {
  --uno-colors-primary: #ff5500; /* 覆盖主色调 */
  --uno-colors-background: #f9f9f9; /* 覆盖背景色 */
}

.dark {
  --uno-colors-primary: #ffaa77; /* 覆盖暗色模式主色调 */
  --uno-colors-background: #111111; /* 覆盖暗色模式背景色 */
}
```

## 第三方集成

川流主题支持多种第三方服务集成，您还可以添加其他服务。

### 添加 Google Analytics

川流主题已经支持 Google Analytics，您只需在配置文件中添加您的跟踪 ID：

```typescript
analytics: {
  googleAnalyticsId: 'G-XXXXXXXXXX', // 替换为您的Google Analytics ID
},
```

### 添加自定义字体

要使用自定义字体，您可以：

1. 将字体文件放在 `public/fonts/` 目录下
2. 创建 CSS 文件定义字体
3. 在配置文件中更新字体设置

```css
/* src/styles/fonts.css */
@font-face {
  font-family: 'CustomFont';
  src: url('/fonts/CustomFont.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
```

然后在配置文件中使用：

```typescript
appearance: {
  fonts: {
    header: '"CustomFont", serif',
    ui: '"CustomFont", sans-serif',
  },
},
```

### 添加自定义 JavaScript

如果您需要添加自定义 JavaScript 代码，可以：

1. 创建 JavaScript 文件
2. 在布局中导入

```javascript
// src/scripts/custom.js
document.addEventListener('DOMContentLoaded', () => {
  console.log('自定义脚本已加载');
  
  // 添加您的自定义功能
  const buttons = document.querySelectorAll('.custom-button');
  buttons.forEach(button => {
    button.addEventListener('click', () => {
      alert('按钮被点击了！');
    });
  });
});
```

然后在布局中导入：

```astro
---
// src/layouts/LayoutDefault.astro
---

<script src="../scripts/custom.js"></script>
```

### 集成其他评论系统

如果您想使用川流主题不支持的评论系统，可以创建自定义评论组件：

1. 创建评论组件
2. 修改 `Comments.astro` 组件

```astro
---
// src/components/comments/CustomComment.astro
---

<div id="custom-comment-container"></div>

<script>
  // 初始化您的自定义评论系统
  document.addEventListener('DOMContentLoaded', () => {
    // 加载评论系统
    const script = document.createElement('script');
    script.src = 'https://your-comment-system.com/embed.js';
    script.async = true;
    document.body.appendChild(script);
    
    // 配置评论系统
    window.customCommentConfig = {
      container: 'custom-comment-container',
      siteId: 'your-site-id',
    };
  });
</script>
```

然后修改 `Comments.astro` 组件：

```astro
---
// src/components/Comments.astro
import CustomComment from './comments/CustomComment.astro';
// ...其他导入
---

{
  provider === 'custom' && <CustomComment />
  // ...其他评论系统
}
```

## 高级功能开发

如果您想添加全新的功能，可能需要更深入的开发工作。以下是一些示例：

### 添加暗色模式切换按钮

```astro
---
// src/components/ThemeToggle.astro
---

<button id="theme-toggle" aria-label="切换暗色模式">
  <span class="i-mdi-weather-sunny light-icon"></span>
  <span class="i-mdi-weather-night dark-icon"></span>
</button>

<style>
  #theme-toggle {
    border: none;
    background: transparent;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .light-icon {
    display: var(--light-icon-display, block);
  }
  
  .dark-icon {
    display: var(--dark-icon-display, none);
  }
  
  :global(.dark) .light-icon {
    display: none;
  }
  
  :global(.dark) .dark-icon {
    display: block;
  }
</style>

<script>
  const toggle = document.getElementById('theme-toggle');
  
  if (toggle) {
    toggle.addEventListener('click', () => {
      const isDark = document.documentElement.classList.toggle('dark');
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
    });
  }
</script>
```

然后在 `Header.astro` 中使用这个组件。

### 添加目录（TOC）组件

```astro
---
// src/components/TableOfContents.astro
interface Props {
  headings: { depth: number; slug: string; text: string }[];
}

const { headings } = Astro.props;
---

{headings.length > 0 && (
  <div class="toc-container">
    <h2 class="toc-title">目录</h2>
    <ul class="toc-list">
      {headings.map((heading) => (
        <li class={`toc-item toc-depth-${heading.depth}`}>
          <a href={`#${heading.slug}`}>{heading.text}</a>
        </li>
      ))}
    </ul>
  </div>
)}

<style>
  .toc-container {
    border: 1px solid #eee;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 2rem;
  }
  
  .toc-title {
    margin-top: 0;
    font-size: 1.25rem;
  }
  
  .toc-list {
    list-style: none;
    padding-left: 0;
  }
  
  .toc-item {
    margin-bottom: 0.5rem;
  }
  
  .toc-depth-2 {
    padding-left: 0;
  }
  
  .toc-depth-3 {
    padding-left: 1rem;
  }
  
  .toc-depth-4 {
    padding-left: 2rem;
  }
</style>
```

然后在 `LayoutPost.astro` 中使用：

```astro
---
import TableOfContents from '../components/TableOfContents.astro';

// 获取文章内容中的标题
const headings = Astro.props.headings || [];
---

<TableOfContents headings={headings} />
```

## 下一步

现在您已经了解了如何进行高级自定义，接下来可以：

1. [解决常见问题](./07-troubleshooting.md)
2. 回到[基础入门](./01-getting-started.md)，重新查看整个流程
3. 探索 [Astro 文档](https://docs.astro.build/)，了解更多高级功能
