/**
 * 自定义宽度样式
 * 用于统一所有页面的宽度
 */

/* 修改主体布局宽度 */
body {
  --content-width: 700px; /* 定义内容区域的统一宽度 */
}

/* 自定义布局类 */
.custom-layout {
  max-width: 1200px; /* 整体最大宽度 */
}

/* 平板和移动设备居中样式 */
.tablet-centered {
  width: 100%;
  max-width: var(--content-width);
  margin: 0 auto;
  padding: 0 1rem;
}

/* 容器样式 */
.container {
  width: 100%;
  max-width: var(--content-width);
  margin: 0 auto;
  padding: 0 1rem;
}

/* 文章内容宽度控制 - 覆盖UnoCSS的Typography预设 */
.prose {
  width: 100%;
  max-width: 100% !important; /* 覆盖Typography预设的默认最大宽度 */
}

/* 首页和文章详情页特殊处理 */
.tablet-centered .prose {
  width: 100%;
}

/* 强制覆盖UnoCSS Typography预设的宽度限制 */
:where(.prose) {
  max-width: none !important;
}

/* 桌面设备样式 - 调整网格布局 */
@media (min-width: 1024px) {
  /* 调整主内容区域宽度 */
  main {
    max-width: 100%; /* 填充网格单元格 */
  }

  /* 确保内容区域在网格布局中也保持一致宽度 */
  main .tablet-centered,
  main .container {
    max-width: var(--content-width); /* 在主内容区域内部保持统一宽度 */
    margin: 0 auto;
  }

  /* 首页和文章详情页特殊处理 */
  main .tablet-centered .prose {
    max-width: 100% !important;
  }

  /* 调整网格布局比例，确保主内容区域有足够空间 */
  .custom-layout {
    grid-template-columns: minmax(var(--content-width), 3fr) 1fr;
  }
}
