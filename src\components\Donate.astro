---
// 导入配置
import { themeConfig } from '~/.config'

// 获取翻译函数
const { translate: t } = Astro.locals

// 组件属性
interface Props {
  title?: string
  description?: string
  paypalLink?: string
  cryptoAddresses?: {
    btc?: string
    eth?: string
    usdt?: string
    [key: string]: string | undefined
  }
}

// 从配置中获取赞赏设置
const donateConfig = themeConfig.donate

const {
  title = t('donate') || '赞赏',
  description = t('donate_text') || "请我喝杯咖啡吗？(●'◡'●)",
  paypalLink = donateConfig.paypal || 'https://www.paypal.me/yourusername',
  cryptoAddresses = donateConfig.crypto || {
    btc: 'your-btc-address',
    eth: 'your-eth-address',
    usdt: 'your-usdt-address',
  },
} = Astro.props
---

<div class="donate-container">
  <button id="donate-button" class="donate-button">
    <span class="i-mdi-gift-outline mr-1"></span>
    {title}
  </button>

  <div id="donate-popup" class="donate-popup">
    <div class="donate-header">
      <h3>{title}</h3>
      <p>{description}</p>
    </div>

    <div class="donate-methods">
      <!-- 微信支付 -->
      <div class="donate-method" data-method="wechat">
        <div class="method-icon">
          <span class="i-mdi-wechat donate-icon"></span>
          <span class="donate-method-name">{t('donate_wechat') || '微信支付'}</span>
        </div>
        <div class="method-popup">
          <img src="/images/wechat-qrcode.png" alt="WeChat Pay QR Code" loading="lazy" />
          <p>{t('donate_wechat') || '微信支付'}</p>
        </div>
      </div>

      <!-- 支付宝 -->
      <div class="donate-method" data-method="alipay">
        <div class="method-icon">
          <span class="i-mdi-alpha-a-circle donate-icon"></span>
          <span class="donate-method-name">{t('donate_alipay') || '支付宝'}</span>
        </div>
        <div class="method-popup">
          <img src="/images/alipay-qrcode.png" alt="Alipay QR Code" loading="lazy" />
          <p>{t('donate_alipay') || '支付宝'}</p>
        </div>
      </div>

      <!-- PayPal -->
      <div class="donate-method" data-method="paypal">
        <a
          href={paypalLink}
          target="_blank"
          rel="noopener noreferrer"
          class="method-icon paypal-link not-underline-hover"
        >
          <span class="i-mdi-paypal donate-icon"></span>
          <span class="donate-method-name">{t('donate_paypal') || 'PayPal'}</span>
        </a>
      </div>

      <!-- 加密货币 -->
      <div class="donate-method" data-method="crypto">
        <div class="method-icon">
          <span class="i-mdi-bitcoin donate-icon"></span>
          <span class="donate-method-name">{t('donate_crypto') || '加密货币'}</span>
        </div>
        <div class="method-popup crypto-popup">
          <div class="crypto-address">
            <div class="crypto-row">
              <span>BTC:</span>
              <code id="btc-address">{cryptoAddresses.btc}</code>
              <button class="copy-btn" data-address="btc">
                <span class="i-mdi-content-copy"></span>
              </button>
            </div>
            <div class="crypto-row">
              <span>ETH:</span>
              <code id="eth-address">{cryptoAddresses.eth}</code>
              <button class="copy-btn" data-address="eth">
                <span class="i-mdi-content-copy"></span>
              </button>
            </div>
            <div class="crypto-row">
              <span>USDT:</span>
              <code id="usdt-address">{cryptoAddresses.usdt}</code>
              <button class="copy-btn" data-address="usdt">
                <span class="i-mdi-content-copy"></span>
              </button>
            </div>
          </div>
          <p>{t('donate_crypto') || '加密货币'}</p>
        </div>
      </div>
    </div>

    <div class="donate-footer">
      <p>{t('donate_thanks') || '感谢您的支持！'}</p>
    </div>
  </div>
</div>

<style>
  .donate-container {
    position: relative;
    margin: 2rem 0;
    text-align: center;
    z-index: 10;
  }

  .donate-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1.5rem;
    background-color: var(--uno-colors-primary, #2e405b);
    color: var(--uno-colors-background, #ffffff);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .donate-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
  }

  .donate-popup {
    position: absolute;
    bottom: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%);
    width: 320px;
    background-color: var(--uno-colors-background, #ffffff);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 1.5rem;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .donate-popup.active {
    opacity: 1;
    visibility: visible;
  }

  /* 关闭按钮样式已移除 */

  .donate-header {
    margin-bottom: 1rem;
    text-align: center;
    position: relative;
  }

  .donate-header h3 {
    margin: 0 0 0.5rem;
    font-size: 1.25rem;
  }

  .donate-header p {
    margin: 0;
    font-size: 0.9rem;
    color: #666;
  }

  .dark .donate-header p {
    color: #aaa;
  }

  .donate-methods {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  /* 移动端赞赏按钮布局优化 */
  @media (max-width: 480px) {
    .donate-methods {
      gap: 0.5rem; /* 减小间距 */
    }
  }

  .donate-method {
    position: relative;
    width: calc(50% - 0.5rem);
    text-align: center;
  }

  .method-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.75rem;
    border-radius: 4px;
    background-color: #f5f5f5;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-decoration: none;
    color: inherit;
  }

  .dark .method-icon {
    background-color: #333;
  }

  .method-icon:hover {
    background-color: #eaeaea;
  }

  .dark .method-icon:hover {
    background-color: #444;
  }

  /* PayPal按钮悬浮效果 */
  .paypal-link:hover {
    background-color: #eaeaea;
    transform: translateY(-2px);
  }

  .dark .paypal-link:hover {
    background-color: #444;
  }

  .donate-icon {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
  }

  .donate-method-name {
    font-size: 0.8rem;
  }

  .method-popup {
    position: absolute;
    top: -220px;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 30;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    width: 220px;
  }

  .dark .method-popup {
    background-color: #222;
  }

  .method-popup img {
    width: 180px;
    height: 180px;
    display: block;
    margin: 0 auto 0.5rem;
  }

  .method-popup p {
    margin: 0;
    font-size: 0.9rem;
    text-align: center;
  }

  .donate-method.active .method-popup {
    opacity: 1;
    visibility: visible;
  }

  .crypto-popup {
    width: 280px;
  }

  .crypto-address {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .crypto-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
  }

  .crypto-row span {
    flex-shrink: 0;
    margin-right: 0.5rem;
    font-weight: bold;
  }

  .crypto-row code {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    background-color: #f0f0f0;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
  }

  .dark .crypto-row code {
    background-color: #333;
  }

  .copy-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: 0.25rem;
    color: #666;
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .copy-btn:hover {
    background-color: #eaeaea;
    color: #333;
  }

  .dark .copy-btn:hover {
    background-color: #444;
    color: #eee;
  }

  .donate-footer {
    text-align: center;
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
  }

  .dark .donate-footer {
    color: #aaa;
  }

  /* 响应式调整 */
  @media (max-width: 480px) {
    .donate-popup {
      width: 280px;
    }

    .donate-method {
      width: calc(50% - 0.5rem); /* 保持两列布局 */
    }

    /* 缩小按钮尺寸 */
    .method-icon {
      padding: 0.5rem;
    }

    .donate-icon {
      font-size: 1.2rem;
      margin-bottom: 0.15rem;
    }

    .donate-method-name {
      font-size: 0.7rem;
    }

    /* 调整二维码和虚拟货币窗口位置 */
    .method-popup {
      position: absolute;
      top: -200px; /* 调整为赞赏第一行按钮上方 */
      left: 50%;
      transform: translateX(-50%); /* 居中显示 */
      z-index: 100; /* 确保不被其他元素覆盖 */
      width: 200px;
    }

    .method-popup img {
      width: 150px;
      height: 150px;
    }

    /* 加密货币窗口特殊处理 */
    .crypto-popup {
      width: 250px;
    }
  }
</style>

<script is:inline>
  // 立即执行函数确保脚本在加载时就执行
  ;(function () {
    // 复制文本的辅助函数
    function copyTextToClipboard(text, onSuccess, onError) {
      // 首选方法：使用现代Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard
          .writeText(text)
          .then(onSuccess)
          .catch((error) => {
            console.error('使用Clipboard API复制失败:', error)
            // 如果Clipboard API失败，回退到替代方法
            fallbackCopyTextToClipboard(text, onSuccess, onError)
          })
      }
 else {
        // 如果Clipboard API不可用（例如非HTTPS环境），使用回退方法
        fallbackCopyTextToClipboard(text, onSuccess, onError)
      }
    }

    // 回退复制方法（使用选择+异步剪贴板API）
    async function fallbackCopyTextToClipboard(text, onSuccess, onError) {
      try {
        // 创建一个临时的文本区域
        const tempInput = document.createElement('textarea')
        tempInput.style.position = 'absolute'
        tempInput.style.left = '-9999px'
        tempInput.style.top = '0'
        tempInput.value = text
        document.body.appendChild(tempInput)

        // 选择文本
        tempInput.select()
        tempInput.setSelectionRange(0, 99999) // 对于移动设备

        // 尝试使用document.execCommand的替代方案
        try {
          // 尝试使用异步剪贴板API
          await navigator.clipboard.writeText(text)
          document.body.removeChild(tempInput)
          onSuccess()
        }
 catch (clipboardErr) {
          // 如果异步API失败，提示用户手动复制
          console.warn('自动复制失败，请手动复制', clipboardErr)
          // 保持文本选中状态，让用户可以手动复制
          const wasSuccessful = window.prompt('请按Ctrl+C/Command+C复制文本，然后按确定', text)
          document.body.removeChild(tempInput)

          if (wasSuccessful !== null) {
            onSuccess() // 用户点击了确定，假设复制成功
          }
 else {
            onError(new Error('用户取消了手动复制'))
          }
        }
      }
 catch (err) {
        onError(err)
      }
    }

    // 初始化赞赏功能
    function initDonate() {
      // 获取DOM元素
      const donateButton = document.getElementById('donate-button')
      const donatePopup = document.getElementById('donate-popup')
      const donateMethods = document.querySelectorAll('.donate-method')

      // 检查必要元素
      if (!donateButton || !donatePopup) {
        return
      }

      // 点击按钮显示/隐藏弹窗
      donateButton.addEventListener('click', (e) => {
        e.stopPropagation() // 防止事件冒泡

        // 如果弹窗已经显示，则隐藏
        if (donatePopup.classList.contains('active')) {
          donatePopup.classList.remove('active')
          // 移除所有active类
          donateMethods.forEach((m) => {
            m.classList.remove('active')
          })
        }
 else {
          // 否则显示弹窗
          donatePopup.classList.add('active')
        }
      })

      // 关闭按钮事件处理已移除

      // 点击其他区域关闭弹窗
      document.addEventListener('click', (e) => {
        if (donatePopup.classList.contains('active')) {
          const target = e.target
          if (target && !donatePopup.contains(target) && target !== donateButton) {
            console.log('Clicked outside donate popup')
            donatePopup.classList.remove('active')
            // 移除所有active类
            donateMethods.forEach((m) => {
              m.classList.remove('active')
            })
          }
        }
      })

      // 点击支付方式显示/隐藏悬浮窗口
      donateMethods.forEach((method) => {
        if (method.getAttribute('data-method') !== 'paypal') {
          const methodIcon = method.querySelector('.method-icon')
          if (methodIcon) {
            methodIcon.addEventListener('click', (e) => {
              e.stopPropagation() // 防止事件冒泡
              console.log('Method icon clicked')

              // 如果当前方式已激活，则取消激活
              if (method.classList.contains('active')) {
                method.classList.remove('active')
              }
 else {
                // 否则，先移除所有激活状态，再激活当前方式
                donateMethods.forEach((m) => {
                  m.classList.remove('active')
                })
                method.classList.add('active')
              }
            })
          }
        }
      })

      // 复制加密货币地址
      const copyButtons = document.querySelectorAll('.copy-btn')

      copyButtons.forEach((button) => {
        button.addEventListener('click', (e) => {
          e.stopPropagation() // 防止事件冒泡
          console.log('Copy button clicked')

          const type = button.getAttribute('data-address')
          if (type) {
            const addressElement = document.getElementById(`${type}-address`)

            if (addressElement) {
              const textToCopy = addressElement.textContent || ''
              const originalIcon = button.innerHTML

              // 使用辅助函数复制文本
              copyTextToClipboard(
                textToCopy,
                // 成功回调
                () => {
                  // 显示复制成功的反馈
                  button.innerHTML = '<span class="i-mdi-check"></span>'
                  setTimeout(() => {
                    button.innerHTML = originalIcon
                  }, 1500)
                },
                // 失败回调
                (error) => {
                  console.error('复制失败:', error)
                  // 显示复制失败的反馈
                  button.innerHTML = '<span class="i-mdi-close"></span>'
                  setTimeout(() => {
                    button.innerHTML = originalIcon
                  }, 1500)
                },
              )
            }
          }
        })
      })

      console.log('Donate component initialized')
    }

    // 只在DOM内容加载后初始化一次
    document.addEventListener('DOMContentLoaded', initDonate)
  })()
</script>
