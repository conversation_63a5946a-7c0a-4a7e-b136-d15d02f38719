---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import SiteSeo from '~/components/SiteSeo.astro'
import GuestbookComponent from '~/components/Guestbook.astro'
import { themeConfig } from '~/.config'

const { translate: t } = Astro.locals
const guestbookTitle = t('Guestbook') || "留言板"
const guestbookDescription = "欢迎来到我的留言板！这里是一个交流互动的地方，您可以在下方留言，分享您的想法、提出建议、提问或者只是打个招呼。"
---

<LayoutDefault>
  <SiteSeo slot="seo" title={guestbookTitle} desc={guestbookDescription} />

  <!-- 留言板结构化数据 -->
  <script is:inline type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "DiscussionForumPosting",
    "headline": guestbookTitle,
    "description": guestbookDescription,
    "url": new URL("guestbook/", Astro.site).toString(),
    "author": {
      "@type": "Person",
      "name": themeConfig.site.author
    },
    "datePublished": new Date().toISOString()
  })} />

  <GuestbookComponent title={guestbookTitle} />
</LayoutDefault>
