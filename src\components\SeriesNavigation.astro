---
import { getCollection } from 'astro:content';

const { currentPost, postsPerPage = 6 } = Astro.props;

// 生成分页按钮的数据
function generatePaginationData(totalPages: number, currentPage: number) {
  // 如果总页数小于等于7，返回所有页码
  if (totalPages <= 7) {
    return Array.from({ length: totalPages }, (_, i) => ({
      type: 'page',
      page: i + 1,
      isCurrent: i + 1 === currentPage
    }));
  }

  // 否则，返回优化的分页导航数据
  const buttons = [];

  // 始终显示第一页
  buttons.push({
    type: 'page',
    page: 1,
    isCurrent: currentPage === 1
  });

  // 计算需要显示的页码范围
  let startPage = Math.max(2, currentPage - 1);
  let endPage = Math.min(totalPages - 1, currentPage + 1);

  // 调整以确保显示5个页码按钮（包括第一页和最后一页）
  if (currentPage <= 3) {
    // 靠近开始，显示2-5页
    endPage = Math.min(5, totalPages - 1);
  } else if (currentPage >= totalPages - 2) {
    // 靠近结束，显示倒数第5页到倒数第2页
    startPage = Math.max(2, totalPages - 4);
  }

  // 如果第一页和起始页之间有间隔，添加省略号
  if (startPage > 2) {
    buttons.push({ type: 'ellipsis' });
  }

  // 添加中间的页码
  for (let i = startPage; i <= endPage; i++) {
    buttons.push({
      type: 'page',
      page: i,
      isCurrent: i === currentPage
    });
  }

  // 如果结束页和最后一页之间有间隔，添加省略号
  if (endPage < totalPages - 1) {
    buttons.push({ type: 'ellipsis' });
  }

  // 始终显示最后一页
  buttons.push({
    type: 'page',
    page: totalPages,
    isCurrent: currentPage === totalPages
  });

  return buttons;
}
const { series } = currentPost.data;

// 获取状态的CSS类名
function getStatusClass(status: string): string {
  switch (status) {
    case '连载中':
      return 'ongoing';
    case '已完结':
      return 'completed';
    case '计划中':
      return 'planned';
    case '暂停更新':
      return 'paused';
    default:
      return 'ongoing';
  }
}

// 如果不是系列文章，不显示组件
if (!series) return null;

// 获取同系列的所有文章
const allPosts = await getCollection('posts', ({ data }) => {
  return import.meta.env.PROD ? !data.draft : true;
});

const seriesPosts = allPosts
  .filter(post => post.data.series && post.data.series.name === series.name)
  .sort((a, b) => {
    // 添加类型检查
    if (!a.data.series || !b.data.series) return 0;
    return a.data.series.order - b.data.series.order;
  });

// 查找前一篇和后一篇
const currentIndex = seriesPosts.findIndex(post => post.id === currentPost.id);
const prevPost = currentIndex > 0 ? seriesPosts[currentIndex - 1] : null;
const nextPost = currentIndex < seriesPosts.length - 1 ? seriesPosts[currentIndex + 1] : null;

// 分页相关变量
const totalPosts = seriesPosts.length;
const totalPages = Math.ceil(totalPosts / postsPerPage);

// 确定当前文章在哪一页
const currentPage = Math.floor(currentIndex / postsPerPage) + 1;

// 获取当前页的文章
const startIndex = (currentPage - 1) * postsPerPage;
const endIndex = Math.min(startIndex + postsPerPage, totalPosts);
const currentPagePosts = seriesPosts.slice(startIndex, endIndex);
---

<div class="series-navigation">
  <div class="series-list-container">
    <div class="series-list-header">
      <div class="series-header">
        <h3>
          <span class="series-title">「{series.name}」系列</span>
          ({currentIndex + 1}/{seriesPosts.length})
        </h3>
        {series.status && (
          <span class={`series-status status-${getStatusClass(series.status)}`}>
            {series.status}
          </span>
        )}
      </div>
      <button class="toggle-series-list" aria-label="展开或折叠文章列表">
        <span class="toggle-icon">▼</span>
      </button>
    </div>
    <div class="series-list collapsed">
      <div class="series-list-content">
        {currentPagePosts.map((post, index) => {
          const globalIndex = startIndex + index;
          return (
            <a
              href={post.data.slug ? `/posts/${post.data.slug}/` : `/posts/${post.id}/`}
              class={post.id === currentPost.id ? "current" : ""}
            >
              {globalIndex + 1}. {post.data.title}
            </a>
          );
        })}
      </div>

      {totalPages > 1 && (
        <div class="series-pages">
          {generatePaginationData(totalPages, currentPage).map(item => {
            if (item.type === 'ellipsis') {
              return <span class="page-ellipsis">...</span>;
            } else {
              return (
                <a
                  href="#"
                  data-page={item.page}
                  class={`page-link ${item.isCurrent ? 'current-page' : ''}`}
                >
                  {item.page}
                </a>
              );
            }
          })}
        </div>
      )}
    </div>
  </div>

  <div class={`series-pagination ${!prevPost || !nextPost ? 'single-button' : ''}`}>
    {prevPost && (
      <a href={prevPost.data.slug ? `/posts/${prevPost.data.slug}/` : `/posts/${prevPost.id}/`} class={`prev-post ${!nextPost ? 'only-button' : ''}`}>
        ← 上一篇: {prevPost.data.title}
      </a>
    )}

    {nextPost && (
      <a href={nextPost.data.slug ? `/posts/${nextPost.data.slug}/` : `/posts/${nextPost.id}/`} class={`next-post ${!prevPost ? 'only-button' : ''}`}>
        下一篇: {nextPost.data.title} →
      </a>
    )}
  </div>
</div>

<script is:inline define:vars={{ seriesPosts, currentPost, postsPerPage }}>
  try {
    // 将所有系列文章数据传递给客户端脚本
    const allSeriesPostsData = seriesPosts.map((post, index) => ({
      id: post.id,
      title: post.data.title,
      slug: post.data.slug,
      order: index + 1,
      isCurrent: post.id === currentPost.id
    }));

    // 初始化分页和折叠功能
    function initSeriesNavigation() {
      const seriesNavigation = document.querySelector('.series-navigation');
      if (!seriesNavigation) return;

      const seriesList = seriesNavigation.querySelector('.series-list');
      if (!seriesList) return;

      // 初始化折叠/展开功能
      const seriesListHeader = seriesNavigation.querySelector('.series-list-header');
      if (seriesListHeader) {
        // 添加事件监听器
        seriesListHeader.addEventListener('click', toggleSeriesList);
      }

      // 获取当前页码
      const currentPage = parseInt(seriesNavigation.querySelector('.page-link.current-page')?.getAttribute('data-page') || '1', 10);

      // 初始化分页导航
      updatePagination(currentPage);

      // 折叠/展开文章列表
      function toggleSeriesList(e) {
        // 如果点击的是链接或分页按钮，不触发折叠/展开
        if (e.target.tagName === 'A' || e.target.closest('.page-link')) {
          return;
        }

        // 阻止默认行为
        e.preventDefault();

        // 获取必要的元素
        const seriesList = seriesNavigation.querySelector('.series-list');
        const toggleIcon = seriesNavigation.querySelector('.toggle-icon');

        // 确保元素存在
        if (!seriesList || !toggleIcon) {
          console.error('找不到必要的元素来切换系列列表');
          return;
        }

        // 切换折叠状态
        if (seriesList.classList.contains('collapsed')) {
          // 展开列表
          seriesList.classList.remove('collapsed');
          toggleIcon.textContent = '▲';
          toggleIcon.setAttribute('aria-label', '折叠文章列表');
        } else {
          // 折叠列表
          seriesList.classList.add('collapsed');
          toggleIcon.textContent = '▼';
          toggleIcon.setAttribute('aria-label', '展开文章列表');
        }
      }

      // 生成分页按钮的数据
      function generatePaginationData(totalPages, currentPage) {
        // 如果总页数小于等于7，返回所有页码
        if (totalPages <= 7) {
          return Array.from({ length: totalPages }, (_, i) => ({
            type: 'page',
            page: i + 1,
            isCurrent: i + 1 === currentPage
          }));
        }

        // 否则，返回优化的分页导航数据
        const buttons = [];

        // 始终显示第一页
        buttons.push({
          type: 'page',
          page: 1,
          isCurrent: currentPage === 1
        });

        // 计算需要显示的页码范围
        let startPage = Math.max(2, currentPage - 1);
        let endPage = Math.min(totalPages - 1, currentPage + 1);

        // 调整以确保显示5个页码按钮（包括第一页和最后一页）
        if (currentPage <= 3) {
          // 靠近开始，显示2-5页
          endPage = Math.min(5, totalPages - 1);
        } else if (currentPage >= totalPages - 2) {
          // 靠近结束，显示倒数第5页到倒数第2页
          startPage = Math.max(2, totalPages - 4);
        }

        // 如果第一页和起始页之间有间隔，添加省略号
        if (startPage > 2) {
          buttons.push({ type: 'ellipsis' });
        }

        // 添加中间的页码
        for (let i = startPage; i <= endPage; i++) {
          buttons.push({
            type: 'page',
            page: i,
            isCurrent: i === currentPage
          });
        }

        // 如果结束页和最后一页之间有间隔，添加省略号
        if (endPage < totalPages - 1) {
          buttons.push({ type: 'ellipsis' });
        }

        // 始终显示最后一页
        buttons.push({
          type: 'page',
          page: totalPages,
          isCurrent: currentPage === totalPages
        });

        return buttons;
      }

      // 更新分页导航
      function updatePagination(currentPage) {
        const seriesPagesContainer = seriesNavigation.querySelector('.series-pages');
        if (!seriesPagesContainer) return;

        // 计算总页数
        const totalPages = Math.ceil(allSeriesPostsData.length / postsPerPage);

        // 生成新的分页数据
        const paginationData = generatePaginationData(totalPages, currentPage);

        // 清空当前分页导航
        seriesPagesContainer.innerHTML = '';

        // 重新渲染分页导航
        paginationData.forEach(item => {
          if (item.type === 'ellipsis') {
            const ellipsis = document.createElement('span');
            ellipsis.className = 'page-ellipsis';
            ellipsis.textContent = '...';
            seriesPagesContainer.appendChild(ellipsis);
          } else {
            const pageLink = document.createElement('a');
            pageLink.href = '#';
            pageLink.setAttribute('data-page', item.page);

            // 应用与原始样式相同的类名
            if (item.isCurrent) {
              pageLink.className = 'page-link current-page';
            } else {
              pageLink.className = 'page-link';
            }

            pageLink.textContent = item.page;

            // 添加点击事件
            pageLink.addEventListener('click', handlePageClick);

            // 添加悬停效果
            pageLink.addEventListener('mouseenter', function() {
              if (!item.isCurrent) {
                const isDarkMode = document.documentElement.classList.contains('dark');
                const primaryRgb = getComputedStyle(document.documentElement).getPropertyValue('--uno-colors-primary-rgb') || '46, 64, 91';

                if (isDarkMode) {
                  this.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                } else {
                  this.style.backgroundColor = `rgba(${primaryRgb}, 0.1)`;
                }

                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.05)';
              }
            });

            pageLink.addEventListener('mouseleave', function() {
              if (!item.isCurrent) {
                const isDarkMode = document.documentElement.classList.contains('dark');
                const primaryRgb = getComputedStyle(document.documentElement).getPropertyValue('--uno-colors-primary-rgb') || '46, 64, 91';

                if (isDarkMode) {
                  this.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                } else {
                  this.style.backgroundColor = `rgba(${primaryRgb}, 0.05)`;
                }

                this.style.transform = '';
                this.style.boxShadow = '';
              }
            });

            // 应用与原始样式相同的内联样式
            pageLink.style.display = 'inline-flex';
            pageLink.style.alignItems = 'center';
            pageLink.style.justifyContent = 'center';
            pageLink.style.minWidth = '2.25rem';
            pageLink.style.height = '2.25rem';
            pageLink.style.padding = '0 0.5rem';
            pageLink.style.borderRadius = '0.375rem';
            pageLink.style.textDecoration = 'none';
            pageLink.style.fontSize = '0.9rem';
            pageLink.style.transition = 'all 0.3s ease';

            // 获取CSS变量
            const primaryRgb = getComputedStyle(document.documentElement).getPropertyValue('--uno-colors-primary-rgb') || '46, 64, 91';
            const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--uno-colors-primary') || '#2e405b';

            // 检测是否为暗色模式
            const isDarkMode = document.documentElement.classList.contains('dark');

            if (item.isCurrent) {
              // 当前页样式
              pageLink.style.backgroundColor = primaryColor;
              pageLink.style.color = 'white';
              pageLink.style.borderColor = primaryColor;
              pageLink.style.fontWeight = '500';

              if (isDarkMode) {
                pageLink.style.color = '#232222';
              }
            } else {
              // 普通页码样式
              if (isDarkMode) {
                pageLink.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                pageLink.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                pageLink.style.color = '#eaeaea';
              } else {
                pageLink.style.backgroundColor = `rgba(${primaryRgb}, 0.05)`;
                pageLink.style.color = primaryColor;
                pageLink.style.border = `1px solid rgba(${primaryRgb}, 0.1)`;
              }
            }

            seriesPagesContainer.appendChild(pageLink);
          }
        });
      }

      // 页码点击处理函数
      function handlePageClick(e) {
        e.preventDefault();

        try {
          // 获取点击的页码
          const page = parseInt(this.getAttribute('data-page'), 10);

          // 如果点击当前页，不做任何操作
          if (this.classList.contains('current-page')) return;

          // 计算该页的文章范围
          const startIndex = (page - 1) * postsPerPage;
          const endIndex = Math.min(startIndex + postsPerPage, allSeriesPostsData.length);
          const pagePostsData = allSeriesPostsData.slice(startIndex, endIndex);

          // 渲染该页的文章
          renderPosts(pagePostsData);

          // 更新分页导航
          updatePagination(page);
        } catch (error) {
          console.error('分页处理错误:', error);
        }
      }

      // 渲染文章列表
      function renderPosts(posts) {
        const seriesListContent = seriesList.querySelector('.series-list-content');
        if (!seriesListContent) return;

        // 保存当前的折叠状态
        const isCollapsed = seriesList.classList.contains('collapsed');

        // 清空内容前记住滚动位置
        const scrollTop = seriesList.scrollTop;

        // 清空内容
        seriesListContent.innerHTML = '';

        // 重新添加文章链接，保持原有样式
        posts.forEach((post) => {
          const a = document.createElement('a');
          a.href = post.slug ? `/posts/${post.slug}/` : `/posts/${post.id}/`;

          // 创建序号和标题的文本节点，而不是使用textContent
          // 这样可以避免链接样式变化
          const orderText = document.createTextNode(`${post.order}. `);
          const titleText = document.createTextNode(post.title);

          a.appendChild(orderText);
          a.appendChild(titleText);

          // 设置基本样式
          a.style.textDecoration = 'none';
          a.style.color = 'inherit';

          // 如果是当前文章，添加特殊样式
          if (post.isCurrent) {
            a.classList.add('current');

            // 确保当前文章的样式与CSS中定义的一致
            // 显式设置样式以确保在点击页码后样式保持一致
            a.style.fontWeight = 'bold';
            a.style.color = ''; // 让CSS变量生效

            // 添加背景色和左边框
            const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--uno-colors-primary') || '#2e405b';
            const primaryRgb = getComputedStyle(document.documentElement).getPropertyValue('--uno-colors-primary-rgb') || '46, 64, 91';

            // 检测是否为暗色模式
            const isDarkMode = document.documentElement.classList.contains('dark');

            // 设置背景色
            if (isDarkMode) {
              a.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            } else {
              a.style.backgroundColor = `rgba(${primaryRgb}, 0.1)`;
            }

            // 设置左边框
            a.style.borderLeft = `3px solid ${primaryColor}`;
            a.style.paddingLeft = 'calc(0.5rem - 3px)';
            a.style.position = 'relative';
          }

          seriesListContent.appendChild(a);
        });

        // 如果之前是展开状态，确保保持展开
        if (!isCollapsed) {
          // 确保列表保持展开状态
          seriesList.classList.remove('collapsed');

          // 恢复滚动位置
          setTimeout(() => {
            seriesList.scrollTop = scrollTop;
          }, 0);
        }
      }
    }

    // 页面加载完成后初始化分页
    document.addEventListener('DOMContentLoaded', initSeriesNavigation);
  } catch (error) {
    console.error('初始化系列文章分页时出错:', error);
  }
</script>

<style>
  .series-navigation {
    margin: 2rem 0;
    padding: 1rem;
    border: 1px solid #eaeaea;
    border-radius: 0.5rem;
    background-color: #f9f9f9;
  }

  .dark .series-navigation {
    border-color: #333;
    background-color: #222;
  }

  .series-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
  }

  .series-header h3 {
    margin: 0;
    font-size: 1.1rem;
  }

  .series-title {
    color: inherit;
    font-weight: 500;
  }

  .series-status {
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.85rem;
    margin-left: 0.5rem;
  }

  /* 不同状态的样式 */
  .status-ongoing {
    background-color: #e6f7ff;
    color: #1890ff;
  }

  .dark .status-ongoing {
    background-color: #003a8c;
    color: #91caff;
  }

  .status-completed {
    background-color: #f6ffed;
    color: #52c41a;
  }

  .dark .status-completed {
    background-color: #135200;
    color: #95de64;
  }

  .status-planned {
    background-color: #fff7e6;
    color: #fa8c16;
  }

  .dark .status-planned {
    background-color: #613400;
    color: #ffc069;
  }

  .status-paused {
    background-color: #fff1f0;
    color: #f5222d;
  }

  .dark .status-paused {
    background-color: #5c0011;
    color: #ff7875;
  }

  /* 系列文章列表容器 */
  .series-list-container {
    margin: 1rem 0;
    border: 1px solid #eaeaea;
    border-radius: 0.375rem;
    overflow: hidden;
  }

  .dark .series-list-container {
    border-color: #333;
  }

  /* 系列文章列表标题栏 */
  .series-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #f5f5f5;
    cursor: pointer;
    font-weight: 500;
    border-bottom: 1px solid #eaeaea;
  }

  .dark .series-list-header {
    background-color: #2a2a2a;
    border-bottom-color: #333;
  }

  /* 折叠/展开按钮 */
  .toggle-series-list {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    color: inherit;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
    margin-left: 0.5rem;
  }

  .toggle-series-list:hover {
    opacity: 0.8;
  }

  .toggle-icon {
    display: inline-block;
    transition: transform 0.3s ease;
    pointer-events: none; /* 确保图标不会干扰点击事件 */
  }

  /* 系列文章列表 */
  .series-list {
    display: flex;
    flex-direction: column;
    max-height: 500px;
    overflow-y: auto;
    transition: max-height 0.3s ease, padding 0.3s ease;
  }

  .series-list-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
  }

  /* 折叠状态 */
  .series-list.collapsed {
    max-height: 0;
    overflow: hidden;
  }

  .series-list a {
    padding: 0.25rem 0.5rem;
    text-decoration: none;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    margin-left: -0.5rem;
    color: inherit;
  }

  .series-list a:hover {
    background-color: #eaeaea;
    text-decoration: none;
    color: inherit;
  }

  .dark .series-list a:hover {
    background-color: #333;
    color: inherit;
  }

  .series-list a.current {
    font-weight: bold;
    color: var(--uno-colors-primary);
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.1);
    border-left: 3px solid var(--uno-colors-primary, #2e405b);
    padding-left: calc(0.5rem - 3px);
    position: relative;
  }

  /* 为当前文章链接添加特殊的悬浮样式 */
  .series-list a.current:hover {
    color: var(--uno-colors-primary);
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.15);
    text-decoration: none;
  }

  .dark .series-list a.current {
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: var(--uno-colors-primary, #FFFFFF);
  }

  .dark .series-list a.current:hover {
    background-color: rgba(255, 255, 255, 0.15);
  }

  /* 系列文章分页样式 */
  .series-pages {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 0.5rem 0 1rem;
    padding-top: 0.5rem;
    border-top: 1px solid #eaeaea;
    flex-wrap: wrap;
  }

  .dark .series-pages {
    border-top-color: #333;
  }

  .series-pages a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2.25rem;
    height: 2.25rem;
    padding: 0 0.5rem;
    border-radius: 0.375rem;
    text-decoration: none;
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.05);
    color: var(--uno-colors-primary, #2e405b);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.1);
  }

  .dark .series-pages a {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: #eaeaea;
  }

  .series-pages a:hover {
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.1);
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    text-decoration: none;
  }

  .dark .series-pages a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    text-decoration: none;
  }

  .series-pages a.current-page {
    background-color: var(--uno-colors-primary, #2e405b);
    color: white;
    border-color: var(--uno-colors-primary, #2e405b);
    font-weight: 500;
  }

  .series-pages a.current-page:hover {
    background-color: var(--uno-colors-primary, #2e405b);
    opacity: 0.9;
    color: white;
    transform: none;
    box-shadow: none;
  }

  .dark .series-pages a.current-page {
    background-color: var(--uno-colors-primary, #FFFFFF);
    color: #232222;
    border-color: var(--uno-colors-primary, #FFFFFF);
  }

  .dark .series-pages a.current-page:hover {
    background-color: var(--uno-colors-primary, #FFFFFF);
    opacity: 0.9;
    color: #232222;
  }

  .page-ellipsis {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2rem;
    height: 2rem;
    padding: 0 0.5rem;
    font-size: 0.875rem;
    color: #666;
  }

  .dark .page-ellipsis {
    color: #aaa;
  }

  .series-pagination {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    gap: 1rem;
  }

  /* 当只有一个按钮时的容器样式 */
  .series-pagination.single-button {
    justify-content: flex-start;
  }

  .prev-post, .next-post {
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    color: inherit;
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.05);
    border: 1px solid rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.1);
    flex: 1;
    display: flex;
    align-items: center;
  }

  .prev-post {
    justify-content: flex-start;
  }

  .next-post {
    justify-content: flex-end;
  }

  /* 当只有一个按钮时的样式 */
  .prev-post.only-button,
  .next-post.only-button {
    flex: 0 1 auto;
    max-width: 50%;
  }

  /* 只有下一篇按钮时的对齐方式 */
  .next-post.only-button {
    margin-left: auto;
    margin-right: 0;
  }

  .prev-post:hover, .next-post:hover {
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    text-decoration: none;
    color: inherit;
  }

  .dark .prev-post, .dark .next-post {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .dark .prev-post:hover, .dark .next-post:hover {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    color: inherit;
  }

  @media (max-width: 768px) {
    .series-pagination {
      flex-direction: column;
      gap: 0.5rem;
    }

    /* 移动端上的单个按钮样式 */
    .prev-post.only-button,
    .next-post.only-button {
      max-width: 100%;
      width: auto;
    }

    /* 移动端上只有下一篇按钮时的对齐方式 */
    .next-post.only-button {
      margin-left: 0;
    }
  }
</style>
