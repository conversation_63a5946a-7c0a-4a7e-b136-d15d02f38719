---
title: 置顶文章1 - 最高优先级
pubDate: 2025-04-13
categories: ["示例", "置顶"]
description: "这是一个置顶文章示例，具有最高优先级（pinOrder=1）"
pin: true
pinOrder: 1
---

# 置顶文章1 - 最高优先级

这是一个置顶文章示例，具有最高优先级（pinOrder=1）。在博客首页和文章列表中，这篇文章将显示在所有其他置顶文章之前。

## 置顶功能说明

在文章的frontmatter中添加以下属性可以控制文章的置顶状态和顺序：

```yaml
pin: true       # 设置为true表示文章需要置顶
pinOrder: 1     # 置顶顺序，数字越小优先级越高
```

## 多文章置顶排序规则

当有多篇文章需要置顶时，排序规则如下：

1. 首先按照是否置顶排序（置顶的文章显示在前面）
2. 对于置顶的文章，按照`pinOrder`值排序（值越小越靠前）
3. 如果`pinOrder`值相同，则按照发布日期排序（新的文章在前）

这样，您可以精确控制多篇置顶文章的显示顺序。
