import type { ThemeConfig } from '~/types'

// This is the default configuration for the template, please do not modify it directly.
// You can override this configuration in the `.config/user.ts` file.

export const defaultConfig: ThemeConfig = {
  site: {
    title: '川流',
    subtitle: '一切为了自由',
    author: '川流',
    description: '',
    website: '',
    pageSize: 5,
    socialLinks: [
      {
        name: 'github',
        href: 'https://github.com/moeyua/astro-theme-typography',
      },
      {
        name: 'rss',
        href: '/atom.xml',
      },
      {
        name: 'twitter',
        href: 'https://github.com/moeyua/astro-theme-typography',
      },
      {
        name: 'mastodon',
        href: 'https://github.com/moeyua/astro-theme-typography',
      },
    ],
    navLinks: [
      {
        name: 'Posts',
        href: '/',
      },
      {
        name: 'Archive',
        href: '/archive',
      },
      {
        name: 'Categories',
        href: '/categories',
      },
      {
        name: 'About',
        href: '/about',
      },
    ],
    categoryMap: [{ name: '胡适', path: 'hu-shi' }],
    tagMap: [],
    seriesMap: [],
    footer: [
      '© %year <a target="_blank" href="%website">%author</a>',
      'Theme <a target="_blank" href="https://github.com/Moeyua/astro-theme-typography">Typography</a> by <a target="_blank" href="https://moeyua.com">Moeyua</a>',
      'Proudly published with <a target="_blank" href="https://astro.build/">Astro</a>',
    ],
  },
  appearance: {
    theme: 'system',
    locale: 'zh-cn',
    colorsLight: {
      primary: '#2e405b',
      background: '#ffffff',
    },
    colorsDark: {
      primary: '#FFFFFF',
      background: '#232222',
    },
    fonts: {
      header:
        '"HiraMinProN-W6","Source Han Serif CN","Source Han Serif SC","Source Han Serif TC",serif',
      ui: '"Source Sans Pro","Roboto","Helvetica","Helvetica Neue","Source Han Sans SC","Source Han Sans TC","PingFang SC","PingFang HK","PingFang TC",sans-serif',
    },
  },
  seo: {
    twitter: '@moeyua13',
    meta: [],
    link: [],
  },
  rss: {
    fullText: true,
  },
  comment: {
    // disqus: { shortname: "typography-astro" },
  },
  analytics: {
    googleAnalyticsId: '',
    umamiAnalyticsId: '',
  },
  latex: {
    katex: false,
  },
  donate: {
    enable: false,
    paypal: '',
    crypto: {
      btc: '',
      eth: '',
      usdt: '',
    },
  },

  relatedPosts: {
    enable: true,
    maxPosts: 3,
    randomize: false,
  },

  // 首页系列文章轮播配置
  carousel: {
    enable: true, // 是否启用轮播
    maxItems: 5, // 最多显示几个轮播项
    showArticles: 3, // 每个系列显示前几篇文章
    sortBy: 'latest', // 排序方式：'latest'(最新更新)、'articleCount'(文章数量)或'custom'(自定义)
    autoplay: true, // 是否自动轮播
    autoplaySpeed: 5000, // 自动轮播间隔时间(毫秒)
    // 自定义系列列表，如果设置了此项，将只显示这些系列
    // customSeries: ['Astro系列教程', '川流主题使用教程'],
    // 自定义排序方式，仅当sortBy为'custom'时生效
    // customSort: 'alphabetical', // 可选值：'asc'(升序)、'desc'(降序)、'alphabetical'(字母顺序)、'random'(随机)
    // 设备显示控制，可以控制在不同设备上是否显示轮播
    deviceDisplay: {
      desktop: true, // 桌面端是否显示
      tablet: true, // 平板端是否显示
      mobile: true, // 移动端是否显示
    },
  },

  // 图片优化配置
  imageOptimization: {
    enable: true, // 是否启用图片优化
    quality: 80, // 图片质量(1-100)
    formats: ['webp', 'jpeg'], // 生成的图片格式
    widths: [640, 750, 828, 1080, 1200, 1920], // 响应式图片宽度
    usePlaceholder: true, // 是否使用模糊占位符
    placeholderSize: 60, // 占位符图片宽度
    cdn: {
      enable: false, // 是否启用CDN
      provider: '', // CDN提供商，支持'cloudinary'、'imgix'等
      domain: '', // CDN域名
      options: {}, // CDN特定选项
    },
  },

  // AdSense配置
  adsense: {
    enable: false, // 默认禁用
    clientId: '', // AdSense发布商ID
  },

  // 图片灯箱配置
  lightbox: {
    enable: true, // 是否启用灯箱功能
    animationSpeed: 300, // 动画速度(毫秒)
    bgOpacity: 0.9, // 背景不透明度(0-1)
    zoomStep: 0.25, // 每次缩放步长
    maxZoom: 3, // 最大缩放倍数
    minZoom: 0.5, // 最小缩放倍数
  },

}
