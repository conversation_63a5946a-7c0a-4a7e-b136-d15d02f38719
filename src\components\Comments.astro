---
import { themeConfig } from '~/.config'
import type { Post } from '~/types'
import Disqus from '~/components/comments/Disqus.astro'
import Giscus from '~/components/comments/Giscus.astro'
import Twikoo from '~/components/comments/Twikoo.astro'

interface Props {
  post: Post
}

const { id, data } = Astro.props.post

const path = `/posts/${id}`
const url = `${Astro.site?.href}${path}`

const provider = getProvider()

function getProvider() {
  const result = Object.keys(themeConfig.comment)[0]
  return result || ''
}
---

{
  provider !== '' && (
    <div py-4>
      {provider === 'disqus' && <Disqus identifier={id} url={url} title={data.title} />}
      {provider === 'giscus' && <Giscus />}
      {provider === 'twikoo' && <Twikoo path={path} />}
    </div>
  )
}
