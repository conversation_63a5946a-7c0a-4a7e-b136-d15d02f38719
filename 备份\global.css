:root {
  --uno-colors-primary: theme('colors.primary');
  --uno-colors-background: theme('colors.background');
  --uno-fonts-ui: theme('fontFamily.ui');
  --uno-colors-shadow: theme('colors.shadow');

  /* RGB值用于透明度计算 */
  --uno-colors-primary-rgb: 46, 64, 91; /* 默认为浅色模式的主色调 */
}
html {
  --at-apply: antialiased;
  --at-apply: 'bg-background c-primary font-ui text-shadow-color-shadow';

  text-shadow: 1px 1px 1px var(--uno-colors-shadow);
  background-size: 7px 7px;
  background-image:
    linear-gradient(to right, var(--uno-colors-shadow) 1px, transparent 1px),
    linear-gradient(to bottom, var(--uno-colors-shadow) 1px, transparent 1px);
}

/* 暗色模式的RGB值 */
.dark {
  --uno-colors-primary-rgb: 255, 255, 255; /* 暗色模式的主色调 */
}

:where(a):not(.not-underline-hover) {
  --at-apply: 'p-1.5px';
  --at-apply: 'decoration-underline decoration-0.1em decoration-offset-2px';
  --at-apply: 'ease-in-out duration-300ms';
  --at-apply: 'hover:(c-background bg-primary decoration-primary)';
}

/* 通用页面标题样式 */
.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
  font-family: var(--uno-fonts-ui);
}

.page-description {
  color: #666;
  margin-bottom: 2rem;
  text-align: center;
  font-size: 1.1rem;
}

.dark .page-description {
  color: #aaa;
}

/**
 * 自定义宽度样式
 */

/* 容器样式 */
.container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

/* 强制覆盖UnoCSS Typography预设的宽度限制 */
:where(.prose) {
  max-width: none !important;
}

/* 桌面设备样式 */
@media (min-width: 1024px) {
  /* 调整主内容区域宽度 */
  main {
    max-width: 90%; /* 填充网格单元格 */
  }
}
/* 移动设备样式 */
@media (max-width: 1023px) {
  /* 调整主内容区域宽度 */
  main {
    max-width: 700px; /* 填充网格单元格 */
    margin: 0 auto;
  }
}
