:root {
  --uno-colors-primary: theme('colors.primary');
  --uno-colors-background: theme('colors.background');
  --uno-fonts-ui: theme('fontFamily.ui');
  --uno-colors-shadow: theme('colors.shadow');

  /* RGB值用于透明度计算 */
  --uno-colors-primary-rgb: 46, 64, 91; /* 默认为浅色模式的主色调 */
}

html {
  --at-apply: antialiased;
  --at-apply: 'bg-background c-primary font-ui text-shadow-color-shadow';

  text-shadow: 1px 1px 1px var(--uno-colors-shadow);
  background-size: 7px 7px;
  background-image:
    linear-gradient(to right, var(--uno-colors-shadow) 1px, transparent 1px),
    linear-gradient(to bottom, var(--uno-colors-shadow) 1px, transparent 1px);
}

/* 暗色模式的RGB值 */
.dark {
  --uno-colors-primary-rgb: 255, 255, 255; /* 暗色模式的主色调 */
}

:where(a):not(.not-underline-hover) {
  --at-apply: 'p-1.5px';
  --at-apply: 'decoration-underline decoration-0.1em decoration-offset-2px';
  --at-apply: 'ease-in-out duration-300ms';
  --at-apply: 'hover:(c-background bg-primary decoration-primary)';
}

/* 通用页面标题样式 */
.page-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
  font-family: var(--uno-fonts-ui);
}

.page-description {
  color: #666;
  margin-bottom: 2rem;
  text-align: center;
  font-size: 1.1rem;
}

.dark .page-description {
  color: #aaa;
}

/* 筛选导航统一样式 */
.filter-container {
  margin-bottom: 2rem;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: #f9f9f9;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.dark .filter-container {
  background-color: #2d2d2d;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.filter-selects {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.filter-select-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-width: 200px;
}

.filter-select-container label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #666;
}

.dark .filter-select-container label {
  color: #aaa;
}

.filter-select {
  padding: 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #eaeaea;
  background-color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dark .filter-select {
  background-color: #333;
  border-color: #444;
  color: #eaeaea;
}

.filter-select:hover {
  border-color: var(--uno-colors-primary, #2e405b);
}

.filter-select:focus {
  outline: none;
  border-color: var(--uno-colors-primary, #2e405b);
  box-shadow: 0 0 0 2px rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.2);
}

/**
 * 自定义宽度样式
 */

/* 容器样式 */
.container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 0.5rem;
}

/* 桌面设备样式 */
@media (min-width: 1024px) {
  /* 调整主内容区域宽度 */
  main {
    max-width: 90%; /* 填充网格单元格 */
  }
}
/* 移动设备样式 */
@media (max-width: 1023px) {
  /* 调整主内容区域宽度 */
  main {
    max-width: 700px; /* 填充网格单元格 */
    margin: 0 auto;
  }
}
