---
const { translate: t } = Astro.locals;
---

<a href="/search" class="search-button" title={t('search') || '搜索'}>
  <span class="i-mdi-magnify search-icon"></span>
</a>

<style>
  .search-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    background-color: transparent;
  }

  .search-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark .search-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .search-icon {
    width: 20px;
    height: 20px;
    color: var(--tw-color-primary, #2e405b);
  }

  .dark .search-icon {
    color: #e5e7eb;
  }
</style>
