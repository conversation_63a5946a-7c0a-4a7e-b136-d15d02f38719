---
const { placeholder = "搜索...", value = "" } = Astro.props;
---

<div class="search-container">
  <div class="search-input-wrapper">
    <span class="search-icon i-mdi-magnify"></span>
    <input
      type="text"
      id="search-input"
      placeholder={placeholder}
      value={value}
      autocomplete="off"
    />
    <button id="search-clear" class="search-clear-btn i-mdi-close" style="display: none;"></button>
  </div>
</div>

<style>
  .search-container {
    width: 100%;
    margin-bottom: 1.5rem;
  }

  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-icon {
    position: absolute;
    left: 12px;
    width: 20px;
    height: 20px;
    color: var(--tw-color-primary, #2e405b);
    opacity: 0.7;
  }

  #search-input {
    width: 100%;
    padding: 10px 40px 10px 40px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: white;
  }

  #search-input:focus {
    outline: none;
    border-color: var(--tw-color-primary, #2e405b);
    box-shadow: 0 0 0 2px rgba(46, 64, 91, 0.2);
  }

  .search-clear-btn {
    position: absolute;
    right: 12px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    color: #666;
    opacity: 0.7;
    transition: opacity 0.2s ease;
  }

  .search-clear-btn:hover {
    opacity: 1;
  }

  /* 暗黑模式适配 */
  .dark #search-input {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e5e7eb;
  }

  .dark .search-icon,
  .dark .search-clear-btn {
    color: #e5e7eb;
  }
</style>

<script is:inline>
  // 立即执行函数确保脚本在加载时就执行
  (function() {
    // 处理搜索输入和清除按钮
    function initSearchInput() {
      console.log('Initializing search input');
      const searchInput = document.getElementById('search-input');
      const clearButton = document.getElementById('search-clear');

      if (!searchInput || !clearButton) {
        console.error('Search elements not found');
        return;
      }

      // 初始状态检查
      if (searchInput.value) {
        clearButton.style.display = 'block';
      }

      // 输入时显示/隐藏清除按钮
      searchInput.addEventListener('input', function() {
        console.log('Search input changed:', this.value);
        clearButton.style.display = this.value ? 'block' : 'none';
      });

      // 点击清除按钮
      clearButton.addEventListener('click', function() {
        console.log('Clear button clicked');
        searchInput.value = '';
        clearButton.style.display = 'none';
        searchInput.focus();

        // 触发input事件以更新搜索结果
        searchInput.dispatchEvent(new Event('input'));
      });

      console.log('Search input initialized successfully');
    }

    // 在DOM内容加载后初始化
    document.addEventListener('DOMContentLoaded', initSearchInput);
  })();
</script>
