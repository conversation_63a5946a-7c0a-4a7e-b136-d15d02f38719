/**
 * 断点系统
 *
 * 网站使用以下断点系统:
 * - xs: 480px及以下 - 超小屏幕设备(小型手机)
 * - sm: 768px及以下 - 小屏幕设备(手机)
 * - md: 1023px及以下 - 中等屏幕设备
 * - lg: 1024px及以上 - 大屏幕设备(桌面)
 * - xl: 1280px及以上 - 超大屏幕设备(大型桌面)
 *
 * 布局模式:
 * 1. 移动模式(≤1023px): 单列布局，使用移动导航
 * 2. 桌面模式(≥1024px): 多列网格布局，使用桌面导航
 */

export const breakpoints = {
  xs: 480,  // 超小屏幕
  sm: 768,  // 小屏幕/移动设备
  md: 1023, // 中等屏幕/平板设备
  lg: 1024, // 大屏幕/桌面设备
  xl: 1280  // 超大屏幕
};

// 导出媒体查询字符串，方便在样式中使用
export const mediaQueries = {
  xs: `(max-width: ${breakpoints.xs}px)`,
  sm: `(max-width: ${breakpoints.sm}px)`,
  md: `(max-width: ${breakpoints.md}px)`,
  lg: `(min-width: ${breakpoints.lg}px)`,
  xl: `(min-width: ${breakpoints.xl}px)`
};
