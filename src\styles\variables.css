/**
 * 全局CSS变量定义
 * 包含断点、间距、颜色等全局变量
 */

:root {
  /* 断点系统 */
  --breakpoint-xs: 480px; /* 超小屏幕 */
  --breakpoint-sm: 768px; /* 小屏幕/移动设备 */
  --breakpoint-md: 1023px; /* 中等屏幕/平板设备 */
  --breakpoint-lg: 1024px; /* 大屏幕/桌面设备 */
  --breakpoint-xl: 1280px; /* 超大屏幕 */

  /* 布局模式 */
  --mobile-mode: '(max-width: 1023px)';
  --desktop-mode: '(min-width: 1024px)';

  /* 导航高度 */
  --mobile-nav-height: auto;
  --desktop-nav-height: auto;
}
