---
import { themeConfig } from '~/.config'
import Twikoo from '~/components/comments/Twikoo.astro'

const provider = getProvider()

function getProvider() {
  const result = Object.keys(themeConfig.comment)[0]
  return result || ''
}
---

{
  provider !== '' && (
    <div class="about-comments-container">
      <div class="comments-wrapper">
        {provider === 'twikoo' && <Twikoo path="/about" />}
      </div>
    </div>
  )
}

<style>
  .about-comments-container {
    padding: 0;
  }

  .comments-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--uno-colors-primary, #2e405b);
  }

  .comments-description {
    margin-bottom: 1.5rem;
    color: #666;
    font-size: 0.95rem;
  }

  .dark .comments-description {
    color: #aaa;
  }

  .comments-wrapper {
    padding: 1rem;
    transition: transform 0.3s ease;
  }

  .dark .comments-wrapper {
    /* 暗色模式下无特殊样式 */
  }

  @media (max-width: 768px) {
    .comments-wrapper {
      padding: 1rem;
    }
  }
</style>
