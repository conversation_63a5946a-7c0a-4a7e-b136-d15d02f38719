---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import SiteSeo from '~/components/SiteSeo.astro'
import type { Post } from '~/types'
import { formatDate, getPosts, getCategories, getTags } from '~/utils'

// 注释掉未使用的变量，如果将来需要国际化可以重新启用
// const { translate: t } = Astro.locals

const posts = await getPosts(true)

// 获取所有分类和标签
const categoryMap = await getCategories()
const tagMap = await getTags()

// 获取所有分类名称
const categories = Array.from(categoryMap.keys())

// 获取所有标签名称
const tags = Array.from(tagMap.keys())

// 获取所有年份
const years = Array.from(new Set(posts.map(post =>
  (post.data.pubDate ?? new Date()).getFullYear()
))).sort((a, b) => b - a) // 降序排列

const yearMap = getYearMap(posts)

interface ListItem {
  title: string
  href: string
  date: string
  categories: string[]
  tags: string[]
  year: number
}

function getYearMap(posts: Post[]) {
  const result = new Map<number, ListItem[]>()
  for (const post of posts) {
    const year = (post.data.pubDate ?? new Date()).getFullYear()
    const posts = result.get(year) ?? []
    posts.push(getListItem(post))
    result.set(year, posts)
  }
  return Array.from(result.entries())
}

function getListItem(post: Post): ListItem {
  return {
    title: post.data.title,
    href: `/posts/${post.id}/`,
    date: formatDate(post.data.pubDate, 'MM-DD'),
    categories: post.data.categories || [],
    tags: post.data.tags || [],
    year: (post.data.pubDate ?? new Date()).getFullYear()
  }
}
---

<LayoutDefault>
  <SiteSeo slot="seo" title="文章归档" />

  <div class="container">
    <h1 class="page-title">文章归档</h1>

    <!-- 筛选导航 -->
    <div class="filter-container">
      <div class="filter-selects">
        <div class="filter-select-container">
          <label for="year-filter">按年份筛选：</label>
          <select id="year-filter" class="filter-select">
            <option value="all">全部</option>
            {years.map(year => (
              <option value={year}>{year}</option>
            ))}
          </select>
        </div>

        <div class="filter-select-container">
          <label for="category-filter">按分类筛选：</label>
          <select id="category-filter" class="filter-select">
            <option value="all">全部</option>
            {categories.map(category => (
              <option value={category}>{category}</option>
            ))}
          </select>
        </div>

        <div class="filter-select-container">
          <label for="tag-filter">按标签筛选：</label>
          <select id="tag-filter" class="filter-select">
            <option value="all">全部</option>
            {tags.map(tag => (
              <option value={tag}>{tag}</option>
            ))}
          </select>
        </div>
      </div>
    </div>

    <div class="timeline">
      {
        yearMap.map(([year, posts]) => (
          <div class="timeline-year" data-year={year}>
            <div class="year-header">
              <span class="year-dot"></span>
              <h2 class="year-title">{year}</h2>
            </div>

            <div class="year-posts">
              {posts.map((post) => (
                <a href={post.href} class="post-item not-underline-hover"
                   data-year={post.year}
                   data-categories={post.categories.join(',')}
                   data-tags={post.tags.join(',')}>
                  <div class="post-date">{post.date}</div>
                  <div class="post-title-container">
                    <h3 class="post-title">{post.title}</h3>
                  </div>
                </a>
              ))}
            </div>
          </div>
        ))
      }
    </div>
  </div>
</LayoutDefault>

<style>
  .archive-container {
    max-width: 100%;
    margin: 0 auto;
  }

  /* 使用全局 .page-title 样式，移除自定义样式 */
  .archive-title {
    margin-bottom: 0.5rem;
  }

  .archive-description {
    color: #666;
    margin-bottom: 2rem;
    text-align: center;
  }

  .dark .archive-description {
    color: #aaa;
  }

  .timeline {
    position: relative;
    padding-left: 2rem;
  }

  .timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #eaeaea;
  }

  .dark .timeline::before {
    background-color: #333;
  }

  .timeline-year {
    position: relative;
    margin-bottom: 2rem;
  }

  .year-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }

  .year-dot {
    position: absolute;
    left: -2.5rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: var(--tw-color-primary, #2e405b);
    z-index: 1;
  }

  .dark .year-dot {
    background-color: #e5e7eb;
  }

  .year-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0;
  }

  .year-posts {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .post-item {
    display: flex;
    padding: 1rem;
    border-radius: 8px;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
  }

  .post-item:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-decoration: none;
  }

  .dark .post-item {
    background-color: #2d2d2d;
  }

  .dark .post-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  .post-date {
    min-width: 4rem;
    font-size: 0.9rem;
    color: #666;
  }

  .dark .post-date {
    color: #aaa;
  }

  .post-title-container {
    flex: 1;
  }

  .post-title {
    font-size: 1.1rem;
    margin: 0;
    font-weight: 500;
  }

  /* 动画效果 */
  .timeline-year {
    animation: fadeInUp 0.5s ease-out forwards;
    opacity: 0;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 为每个年份添加递增的动画延迟 */
  .timeline-year:nth-child(1) { animation-delay: 0.1s; }
  .timeline-year:nth-child(2) { animation-delay: 0.2s; }
  .timeline-year:nth-child(3) { animation-delay: 0.3s; }
  .timeline-year:nth-child(4) { animation-delay: 0.4s; }
  .timeline-year:nth-child(5) { animation-delay: 0.5s; }
  .timeline-year:nth-child(n+6) { animation-delay: 0.6s; }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .timeline {
      padding-left: 1.5rem;
    }

    .year-dot {
      left: -2rem;
      width: 0.8rem;
      height: 0.8rem;
    }
  }

  /* 筛选导航样式 */
  .archive-filter {
    margin-bottom: 2rem;
    padding: 1rem;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .dark .archive-filter {
    background-color: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .filter-selects {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .filter-select-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    min-width: 200px;
  }

  .filter-select-container label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #666;
  }

  .dark .filter-select-container label {
    color: #aaa;
  }

  .filter-select {
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #eaeaea;
    background-color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .dark .filter-select {
    background-color: #333;
    border-color: #444;
    color: #eaeaea;
  }

  .filter-select:hover {
    border-color: var(--tw-color-primary, #2e405b);
  }

  .filter-select:focus {
    outline: none;
    border-color: var(--tw-color-primary, #2e405b);
    box-shadow: 0 0 0 2px rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.2);
  }

  /* 响应式设计 - 筛选导航 */
  @media (max-width: 768px) {
    .filter-selects {
      flex-direction: row; /* 保持水平排列 */
      flex-wrap: nowrap; /* 防止换行 */
      gap: 0.5rem; /* 减小间距 */
    }

    .filter-select-container {
      min-width: auto; /* 移除最小宽度限制 */
      width: 33.33%; /* 平均分配宽度 */
    }

    .filter-select-container label {
      font-size: 0.8rem; /* 减小标签字体 */
      white-space: nowrap; /* 防止标签换行 */
    }

    .filter-select {
      padding: 0.35rem; /* 减小内边距 */
      font-size: 0.8rem; /* 减小字体大小 */
    }
  }

  /* 更小屏幕的额外优化 */
  @media (max-width: 480px) {
    .filter-selects {
      gap: 0.25rem; /* 进一步减小间距 */
    }

    .filter-select-container label {
      font-size: 0.75rem; /* 进一步减小标签字体 */
    }

    .filter-select {
      padding: 0.25rem; /* 进一步减小内边距 */
      font-size: 0.75rem; /* 进一步减小字体大小 */
    }
  }
</style>

<script is:inline>
  // 筛选导航功能
  function initArchiveFilter() {
    // 获取下拉框元素
    const yearFilter = document.getElementById('year-filter');
    const categoryFilter = document.getElementById('category-filter');
    const tagFilter = document.getElementById('tag-filter');

    // 文章项
    const postItems = document.querySelectorAll('.post-item');
    const timelineYears = document.querySelectorAll('.timeline-year');

    // 年份筛选
    yearFilter.addEventListener('change', function() {
      // 重置其他筛选器
      categoryFilter.value = 'all';
      tagFilter.value = 'all';

      const selectedYear = this.value;

      if (selectedYear === 'all') {
        // 显示所有年份
        timelineYears.forEach(year => {
          year.style.display = 'block';
        });
        postItems.forEach(item => {
          item.style.display = 'flex';
        });
      } else {
        // 只显示选中年份
        timelineYears.forEach(year => {
          if (year.getAttribute('data-year') === selectedYear) {
            year.style.display = 'block';
          } else {
            year.style.display = 'none';
          }
        });
      }
    });

    // 分类筛选
    categoryFilter.addEventListener('change', function() {
      // 重置其他筛选器
      yearFilter.value = 'all';
      tagFilter.value = 'all';

      const selectedCategory = this.value;

      if (selectedCategory === 'all') {
        // 显示所有文章
        postItems.forEach(item => {
          item.style.display = 'flex';
        });
        timelineYears.forEach(year => {
          year.style.display = 'block';
        });
      } else {
        // 只显示选中分类的文章
        postItems.forEach(item => {
          const categories = item.getAttribute('data-categories').split(',');
          if (categories.includes(selectedCategory)) {
            item.style.display = 'flex';
          } else {
            item.style.display = 'none';
          }
        });

        // 隐藏没有文章的年份
        timelineYears.forEach(year => {
          const yearPosts = year.querySelectorAll('.post-item');
          const hasVisiblePosts = Array.from(yearPosts).some(post => post.style.display !== 'none');
          year.style.display = hasVisiblePosts ? 'block' : 'none';
        });
      }
    });

    // 标签筛选
    tagFilter.addEventListener('change', function() {
      // 重置其他筛选器
      yearFilter.value = 'all';
      categoryFilter.value = 'all';

      const selectedTag = this.value;

      if (selectedTag === 'all') {
        // 显示所有文章
        postItems.forEach(item => {
          item.style.display = 'flex';
        });
        timelineYears.forEach(year => {
          year.style.display = 'block';
        });
      } else {
        // 只显示包含选中标签的文章
        postItems.forEach(item => {
          const tags = item.getAttribute('data-tags').split(',');
          if (tags.includes(selectedTag)) {
            item.style.display = 'flex';
          } else {
            item.style.display = 'none';
          }
        });

        // 隐藏没有文章的年份
        timelineYears.forEach(year => {
          const yearPosts = year.querySelectorAll('.post-item');
          const hasVisiblePosts = Array.from(yearPosts).some(post => post.style.display !== 'none');
          year.style.display = hasVisiblePosts ? 'block' : 'none';
        });
      }
    });
  }

  // 页面加载时初始化
  window.addEventListener('load', initArchiveFilter);

  // 在DOMContentLoaded事件中初始化
  document.addEventListener('DOMContentLoaded', initArchiveFilter);
</script>
