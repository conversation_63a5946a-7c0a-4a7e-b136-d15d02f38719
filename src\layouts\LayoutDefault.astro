---
import { themeConfig } from '~/.config'
import SiteFooter from '~/components/SiteFooter.astro'
import SiteNavigation from '~/components/SiteNavigation.astro'
import SiteTitle from '~/components/SiteTitle.astro'
import SiteSeo from '~/components/SiteSeo.astro'
import LaTeX from '~/components/LaTeX.astro'
import Analytics from '~/components/Analytics.astro'
import ThemeScript from '~/components/ThemeScript.astro'
import AdSense from '~/components/AdSense.astro'
import ImageLightbox from '~/components/ImageLightbox.astro'
import MobileNavigation from '~/components/MobileNavigation.astro'
import '~/styles/variables.css' // 全局CSS变量定义
import '~/styles/global.css'
import '~/styles/card-responsive.css' // 卡片和响应式样式优化
import '~/styles/lightbox.css' // 图片灯箱样式
import '~/styles/code-block-fix.css' // 代码块复制按钮修复
import '~/styles/post-compact.css' // 文章正文页间距 首页文章列表间距

const lang = themeConfig.appearance.locale ?? 'en-us'
const dark = themeConfig.appearance.theme === 'dark'
---

<html lang={lang} class:list={['animation-prepared', { dark }]}>
  <head>
    <slot name="seo"> <SiteSeo /> </slot>
    <LaTeX />
    <Analytics />
    <ThemeScript />
    <AdSense />

    <!-- 网站结构化数据 -->
    <script
      is:inline
      type="application/ld+json"
      set:html={JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: themeConfig.site.title,
        description: themeConfig.site.description,
        url: themeConfig.site.website,
        potentialAction: {
          '@type': 'SearchAction',
          target: {
            '@type': 'EntryPoint',
            urlTemplate: `${themeConfig.site.website}/search?q={search_term_string}`,
          },
          'query-input': 'required name=search_term_string',
        },
      })}
    />
  </head>

  <body
    class="h-100vh max-w-1200px min-w-390px mx-a"
    p="7.5 lg:(y-0 x-20)"
    lg="grid gap-x-6 cols-[3fr_1fr] rows-[1fr_9rem]"
  >
    <!-- 移动导航 - 只在移动设备上显示 -->
    <MobileNavigation />
    <header
      class="desktop-header flex flex-col gap-2.5"
      m="7.5 lg:(x-0 t-20 b-4)"
      lg="row-1-2 col-2-3 justify-between items-start"
    >
      <SiteTitle />
      <SiteNavigation />
    </header>

    <main class="overflow-y-scroll scrollbar-hide outline-none" lg="row-1-3 col-1-2 py-20 ">
      <slot />
    </main>

    <footer class="py-7.5 footer-section" lg="row-2-3 col-2-3">
      <SiteFooter />
    </footer>

    <!-- 图片灯箱组件 -->
    <ImageLightbox />

    <style>
      :global(.container) {
        width: 100%;
      }

      /* 移动端样式（包括平板） */
      @media (max-width: 1023px) {
        /* 在移动设备上隐藏桌面导航 */
        .desktop-header {
          display: none;
        }

        /* 页脚在移动端居中显示 */
        .footer-section {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;
        }

        /* 调整主内容区域的样式 */
        main {
          padding-top: 2.6rem !important; /* 为移动导航栏留出足够空间 */
          overflow-x: hidden !important; /* 防止水平滚动 */
        }
      }
    </style>

    <script>
      document.addEventListener('animationend', removeAnimation, false)
      function removeAnimation() {
        document.documentElement.classList.remove('animation-prepared')
        document.removeEventListener('animationend', removeAnimation, false)
      }
    </script>
    <style>
      /* 页面首次加载动画 */
      @keyframes fade-in-down {
        0% {
          transform: translateY(-1rem);
          opacity: 0;
        }
        100% {
          transform: translateY(0);
          opacity: 1;
        }
      }

      @keyframes fade-in-left {
        0% {
          opacity: 0;
          transform: translateX(1rem);
        }

        100% {
          opacity: 1;
          transform: translateX(0);
        }
      }
    </style>

    <!-- 代码块复制功能 -->
    <script is:inline>
      // 立即执行函数确保脚本在加载时就执行
      ;(function () {
        // 复制文本的辅助函数
        function copyTextToClipboard(text, onSuccess, onError) {
          // 首选方法：使用现代Clipboard API
          if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard
              .writeText(text)
              .then(onSuccess)
              .catch((error) => {
                console.error('使用Clipboard API复制失败:', error)
                // 如果Clipboard API失败，回退到替代方法
                fallbackCopyTextToClipboard(text, onSuccess, onError)
              })
          } else {
            // 如果Clipboard API不可用（例如非HTTPS环境），使用回退方法
            fallbackCopyTextToClipboard(text, onSuccess, onError)
          }
        }

        // 回退复制方法（使用选择+异步剪贴板API）
        async function fallbackCopyTextToClipboard(text, onSuccess, onError) {
          try {
            // 创建一个临时的文本区域
            const tempInput = document.createElement('textarea')
            tempInput.style.position = 'absolute'
            tempInput.style.left = '-9999px'
            tempInput.style.top = '0'
            tempInput.value = text
            document.body.appendChild(tempInput)

            // 选择文本
            tempInput.select()
            tempInput.setSelectionRange(0, 99999) // 对于移动设备

            // 尝试使用document.execCommand的替代方案
            try {
              // 尝试使用异步剪贴板API
              await navigator.clipboard.writeText(text)
              document.body.removeChild(tempInput)
              onSuccess()
            } catch (clipboardErr) {
              // 如果异步API失败，提示用户手动复制
              console.warn('自动复制失败，请手动复制', clipboardErr)
              // 保持文本选中状态，让用户可以手动复制
              const wasSuccessful = window.prompt('请按Ctrl+C/Command+C复制文本,然后按确定', text)
              document.body.removeChild(tempInput)

              if (wasSuccessful !== null) {
                onSuccess() // 用户点击了确定，假设复制成功
              } else {
                onError(new Error('用户取消了手动复制'))
              }
            }
          } catch (err) {
            onError(err)
          }
        }

        // 初始化代码块复制功能
        function initCodeCopy() {
          const codeBlocks = document.querySelectorAll('pre code')

          codeBlocks.forEach((codeBlock) => {
            const pre = codeBlock.parentNode
            if (pre.querySelector('.copy-button')) return

            pre.classList.add('code-container')

            const copyButton = document.createElement('button')
            copyButton.className = 'copy-button'
            copyButton.innerHTML = '<span class="i-mdi-content-copy"></span>'
            copyButton.title = '复制代码'

            copyButton.addEventListener('click', () => {
              const code = codeBlock.textContent
              const originalIcon = copyButton.innerHTML

              copyTextToClipboard(
                code,
                // 成功回调
                () => {
                  copyButton.innerHTML = '<span class="i-mdi-check"></span>'
                  setTimeout(() => {
                    copyButton.innerHTML = originalIcon
                  }, 1500)
                },
                // 失败回调
                (error) => {
                  console.error('复制失败:', error)
                  copyButton.innerHTML = '<span class="i-mdi-close"></span>'
                  setTimeout(() => {
                    copyButton.innerHTML = originalIcon
                  }, 1500)
                },
              )
            })

            pre.appendChild(copyButton)
          })
        }

        // 在DOM内容加载后初始化
        document.addEventListener('DOMContentLoaded', initCodeCopy)
      })()

</script>
  </body>
</html>
