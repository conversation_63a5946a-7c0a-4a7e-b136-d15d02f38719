---
title: "川流主题使用教程（二）：内容管理"
pubDate: 2023-12-26
categories: ["教程"]
tags: ["川流主题", "Astro", "博客", "内容管理"]
description: "川流主题使用教程的第二篇，介绍如何创建和管理文章、使用Front-matter字段、组织分类和标签，以及创建系列文章。"
series:
  name: "川流主题使用教程"
  order: 2
---

# 川流主题使用教程（二）：内容管理

在[上一篇教程](/posts/theme-guide-01-basics)中，我们介绍了川流主题的基础知识。本篇教程将重点讲解如何管理博客内容，包括创建和编辑文章、使用 Front-matter 字段、组织分类和标签，以及创建系列文章。

## 文章创建与编辑

### 创建新文章

在川流主题中，所有文章都存储在 `src/content/posts/` 目录下。创建新文章有两种方法：

#### 方法一：手动创建

1. 在 `src/content/posts/` 目录下创建一个新的 `.md` 文件
2. 文件名将成为文章的 URL 路径（例如，`my-first-post.md` 将对应 `/posts/my-first-post/`）
3. 添加必要的 Front-matter 和文章内容

#### 方法二：使用模板

1. 复制 `src/content/templates/post-template.md` 或 `post-template-simple.md` 到 `src/content/posts/` 目录
2. 重命名文件为您想要的 URL 路径
3. 编辑 Front-matter 和文章内容

### 编辑文章

您可以使用任何文本编辑器或 Markdown 编辑器来编辑文章。推荐使用：

- **VS Code**：搭配 Markdown 插件
- **Typora**：所见即所得的 Markdown 编辑器
- **Obsidian**：功能强大的知识管理工具

## Front-matter 字段详解

Front-matter 是文章开头的 YAML 格式元数据，位于三个连字符 `---` 之间。川流主题支持多种 Front-matter 字段：

### 必填字段

```yaml
---
title: "文章标题"                      # 文章标题（必填）
pubDate: 2023-11-15                   # 发布日期（必填，格式：YYYY-MM-DD）
categories: ["分类1", "分类2"]         # 文章分类（必填，至少一个分类）
---
```

### 常用可选字段

```yaml
---
description: "文章的简短描述，用于摘要和SEO" # 文章描述（推荐填写，利于SEO）
tags: ["标签1", "标签2", "标签3"]      # 文章标签（可选）
author: "作者名称"                     # 文章作者（可选，默认使用站点配置中的作者）
modDate: 2023-11-20                   # 最后修改日期（可选，格式同pubDate）
draft: false                          # 是否为草稿（可选，true=草稿，生产环境不显示）
banner: ./images/banner.jpg           # 文章banner图片（可选，路径相对于文章所在目录）
---
```

### 系列文章字段

```yaml
---
series:
  name: "系列名称"                     # 系列名称
  order: 1                            # 在系列中的顺序
---
```

## Markdown 语法指南

川流主题支持标准 Markdown 语法，以及一些扩展功能：

### 基本语法

```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文本** 和 *斜体文本*

- 无序列表项
- 另一个列表项

1. 有序列表项
2. 另一个有序列表项

[链接文本](https://example.com)

![图片描述](./images/example.jpg)

> 这是一段引用文本
```

### 代码块

````markdown
```javascript
// 代码示例
function hello() {
  console.log("Hello, world!");
}
```
````

## 分类与标签管理

川流主题支持文章分类和标签，帮助您组织内容并提高可发现性。

### 分类系统

分类是对文章的主要分组，每篇文章必须至少属于一个分类。分类通常代表文章的主题领域，如"技术"、"生活"等。

在 `src/.config/user.ts` 中配置分类映射：

```typescript
categoryMap: [
  { name: '技术', path: 'technology' },
  { name: '生活', path: 'life' },
  { name: '思考', path: 'thoughts' },
  // 可以添加更多分类...
],
```

### 标签系统

标签是对文章内容的更细粒度描述，一篇文章可以有多个标签或没有标签。标签通常代表文章中提到的具体技术、工具或概念。

在 `src/.config/user.ts` 中配置标签映射：

```typescript
tagMap: [
  { name: '编程', path: 'programming' },
  { name: '旅行', path: 'travel' },
  { name: '阅读', path: 'reading' },
  // 可以添加更多标签...
],
```

## 系列文章创建

系列文章是一组相关联的文章，按照特定顺序排列。川流主题提供了完善的系列文章功能。

### 创建系列文章

1. 在每篇属于同一系列的文章中添加系列信息：

```yaml
---
series:
  name: "Astro系列教程"  # 系列名称（必须完全相同）
  order: 1              # 在系列中的顺序（从1开始）
---
```

2. 确保同一系列的所有文章使用完全相同的系列名称（包括大小写和空格）
3. 使用 `order` 字段指定文章在系列中的顺序

### 系列文章导航

系统会自动在系列文章页面添加系列导航组件，显示：

- 系列中的所有文章列表
- 当前文章在系列中的位置
- 上一篇和下一篇文章的链接

## 下一步

在下一篇教程中，我们将介绍[主题定制](/posts/theme-guide-03-customization)，包括如何设置网站基本信息、自定义颜色和样式、配置导航菜单等。

敬请期待！
