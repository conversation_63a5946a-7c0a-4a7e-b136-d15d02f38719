---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import SiteSeo from '~/components/SiteSeo.astro'
---

<LayoutDefault>
  <SiteSeo slot="seo" title="404 - 页面未找到" desc="您访问的页面不存在" />

  <div class="not-found-container flex flex-col items-center justify-center text-center p-8 my-10">
    <h1 class="text-8xl font-bold mb-4">404</h1>
    <h2 class="text-3xl font-bold mb-6">页面未找到</h2>
    <p class="text-xl mb-8 max-w-md">抱歉，您访问的页面不存在或已被移动到其他位置。</p>

    <div class="suggestions mb-10">
      <h3 class="text-xl font-bold mb-4">您可以尝试：</h3>
      <ul class="flex flex-col gap-2">
        <li>• 检查URL是否正确</li>
        <li>• 返回<a href="/" class="underline hover:opacity-80 transition-opacity">首页</a></li>
        <li>• 使用<a href="/search" class="underline hover:opacity-80 transition-opacity">搜索功能</a>查找内容</li>
        <li>• 浏览<a href="/posts" class="underline hover:opacity-80 transition-opacity">文章归档</a></li>
      </ul>
    </div>

    <a href="/" class="back-home-btn px-6 py-3 rounded-lg transition-all duration-300 hover:scale-105">
      返回首页
    </a>
  </div>
</LayoutDefault>

<style>
  .not-found-container {
    min-height: 50vh;
    animation: fade-in-down 0.8s ease-out;
  }

  .back-home-btn {
    background-color: var(--uno-colors-primary);
    color: var(--uno-colors-background);
    box-shadow: 0 4px 6px rgba(var(--uno-colors-primary-rgb), 0.25);
  }

  .dark .back-home-btn {
    box-shadow: 0 4px 6px rgba(var(--uno-colors-primary-rgb), 0.5);
  }

  @keyframes fade-in-down {
    0% {
      opacity: 0;
      transform: translateY(-20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .not-found-container {
      padding: 1rem;
    }

    h1 {
      font-size: 5rem;
    }

    h2 {
      font-size: 1.5rem;
    }

    p {
      font-size: 1rem;
    }
  }
</style>
