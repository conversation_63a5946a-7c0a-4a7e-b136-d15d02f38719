---
title: "川流主题使用教程（一）：基础入门"
pubDate: 2023-12-25
categories: ["教程"]
tags: ["川流主题", "Astro", "博客"]
description: "川流主题使用教程的第一篇，介绍项目结构、开发环境设置和基本命令使用。"
series:
  name: "川流主题使用教程"
  order: 1
---

# 川流主题使用教程（一）：基础入门

欢迎使用川流主题！这是一个基于 Astro 构建的优雅博客主题，专为中文内容创作者设计。本系列教程将帮助您了解主题的所有功能和自定义选项，即使您没有技术背景，也能轻松上手。

在这第一篇教程中，我们将介绍项目结构、开发环境设置和基本命令使用。

## 项目结构概览

川流主题基于 Astro 构建，项目结构清晰明了：

```
川流主题/
├── public/          # 静态资源目录（图片、字体等）
├── src/             # 源代码目录
│   ├── .config/     # 主题配置文件
│   ├── components/  # 组件目录
│   ├── content/     # 内容目录（文章、页面等）
│   ├── layouts/     # 布局模板
│   ├── pages/       # 页面路由
│   ├── styles/      # 样式文件
│   └── utils/       # 工具函数
├── astro.config.ts  # Astro 配置文件
├── package.json     # 项目依赖
└── tsconfig.json    # TypeScript 配置
```

### 重要目录说明

- **src/content/posts/**：存放所有博客文章的 Markdown 文件
- **src/.config/user.ts**：主题的用户配置文件，大部分自定义设置都在这里
- **src/pages/**：网站的页面路由，每个文件对应一个页面
- **public/**：存放静态资源，如图片、字体等

## 开发环境设置

### 前提条件

在开始之前，请确保您的电脑上已安装：

1. **Node.js**（推荐 v18.0.0 或更高版本）
2. **npm**、**yarn** 或 **pnpm** 包管理器

### 安装步骤

1. **克隆项目**（如果您是从头开始）：

```bash
# 使用 Git 克隆项目
git clone https://github.com/yourusername/your-blog-repo.git my-blog

# 进入项目目录
cd my-blog
```

2. **安装依赖**：

```bash
# 使用 npm
npm install
```

## 基本命令使用

### 开发服务器

启动本地开发服务器，实时预览您的更改：

```bash
npm run dev
```

启动后，您可以在浏览器中访问 `http://localhost:4321` 查看您的网站。

### 构建网站

生成用于部署的静态文件：

```bash
npm run build
```

构建完成后，生成的文件将位于 `dist/` 目录中。

## 下一步

在下一篇教程中，我们将详细介绍[内容管理](/posts/theme-guide-02-content)，包括如何创建和编辑文章、使用 Front-matter 字段、管理分类和标签，以及创建系列文章。

敬请期待！
