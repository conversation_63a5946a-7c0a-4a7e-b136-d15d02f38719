import type { Post } from '~/types'
import { getCollection } from 'astro:content'
import dayjs from 'dayjs'
import MarkdownIt from 'markdown-it'
import sanitizeHtml from 'sanitize-html'

export async function getCategories() {
  const posts = await getPosts()
  const categories = new Map<string, Post[]>()

  for (const post of posts) {
    if (post.data.categories) {
      for (const c of post.data.categories) {
        const posts = categories.get(c) || []
        posts.push(post)
        categories.set(c, posts)
      }
    }
  }

  return categories
}

export async function getTags() {
  const posts = await getPosts()
  const tags = new Map<string, Post[]>()

  for (const post of posts) {
    if (post.data.tags) {
      for (const tag of post.data.tags) {
        const posts = tags.get(tag) || []
        posts.push(post)
        tags.set(tag, posts)
      }
    }
  }

  return tags
}

export function getPathFromTag(
  tag: string,
  tag_map?: { name: string, path: string }[],
) {
  // 如果有映射表，先查找映射
  if (tag_map) {
    const mappingPath = tag_map.find(l => l.name === tag)
    if (mappingPath) return mappingPath.path
  }

  // 如果没有找到映射，使用默认的URL友好转换
  return tag.toLowerCase().replace(/\s+/g, '-')
}

export async function getPosts(isArchivePage = false) {
  const posts = await getCollection('posts')

  posts.sort((a, b) => {
    if (isArchivePage) {
      return dayjs(a.data.pubDate).isBefore(dayjs(b.data.pubDate)) ? 1 : -1
    }

    const aDate = a.data.modDate ? dayjs(a.data.modDate) : dayjs(a.data.pubDate)
    const bDate = b.data.modDate ? dayjs(b.data.modDate) : dayjs(b.data.pubDate)

    return aDate.isBefore(bDate) ? 1 : -1
  })

  if (import.meta.env.PROD) {
    return posts.filter(post => post.data.draft !== true)
  }

  return posts
}

const parser = new MarkdownIt()
export function getPostDescription(post: Post) {
  // 1. 优先使用自定义摘要字段 summary（如果存在）
  if ((post.data as any).summary) {
    return (post.data as any).summary
  }

  // 2. 如果没有summary，直接从正文中提取摘要
  const html = parser.render(post.body || '')
  const sanitized = sanitizeHtml(html, { allowedTags: [] })
  return sanitized.slice(0, 400)
}

export function formatDate(date: Date, format: string = 'YYYY-MM-DD') {
  return dayjs(date).format(format)
}

export function getPathFromCategory(
  category: string,
  category_map: { name: string, path: string }[],
) {
  // 先在映射表中查找
  const mappingPath = category_map.find(l => l.name === category)
  if (mappingPath) return mappingPath.path

  // 如果没有找到映射，生成URL友好的路径
  // 与标签保持一致的处理方式
  return category.toLowerCase().replace(/\s+/g, '-')
}

export function getPathFromSeries(
  series: string,
  series_map?: { name: string, path: string }[],
) {
  // 如果有映射表，先查找映射
  if (series_map) {
    const mappingPath = series_map.find(l => l.name === series)
    if (mappingPath) return mappingPath.path
  }

  // 如果没有找到映射，使用默认的URL友好转换
  return series.toLowerCase().replace(/\s+/g, '-')
}
