import { defineMiddleware } from 'astro:middleware'
import { themeConfig } from '~/.config'
import { LANGUAGES } from '~/i18n.ts'

export const onRequest = defineMiddleware(async (context, next) => {
  const locale = themeConfig.appearance.locale

  const localeTranslate = LANGUAGES[locale]

  function validateKey(key: string): key is keyof typeof localeTranslate {
    return key in localeTranslate
  }

  context.locals.translate = (key, param) => {
    if (!validateKey(key))
      return key
    if (!param)
      return localeTranslate[key]
    return localeTranslate[key].replace('%d', param.toString())
  }

  // 添加URL规范化处理
  const url = new URL(context.request.url)
  const path = url.pathname

  // 如果路径不是静态资源且不以斜杠结尾
  if (!path.includes('.') && !path.endsWith('/') && path !== '') {
    // 重定向到带斜杠的URL
    return Response.redirect(`${url.origin}${path}/`, 301)
  }

  return next()
})
