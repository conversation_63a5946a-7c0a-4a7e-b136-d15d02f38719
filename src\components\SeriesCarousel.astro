---
import { getCollection } from 'astro:content';
import type { CollectionEntry } from 'astro:content';
import { themeConfig } from '~/.config';
import { getPathFromSeries } from '~/utils';

// 定义系列类型
interface Series {
  name: string;
  posts: CollectionEntry<'posts'>[];
  lastUpdated: Date;
  status: string;
}

// 获取轮播配置
const carouselConfig = themeConfig.carousel || {};
const maxItems = carouselConfig.maxItems || 5; // 默认显示5个轮播项
const showArticles = carouselConfig.showArticles || 3; // 默认显示前3篇文章

// 获取所有非草稿文章
const allPosts = await getCollection('posts', ({ data }) => {
  return import.meta.env.PROD ? !data.draft : true;
});

// 创建系列映射
const seriesMap = new Map<string, Series>();

// 整理系列信息
allPosts.forEach(post => {
  if (post.data.series) {
    const { name, status } = post.data.series;
    if (!seriesMap.has(name)) {
      seriesMap.set(name, {
        name,
        posts: [],
        lastUpdated: new Date(0),
        status: status || '连载中' // 默认为连载中
      });
    }

    const series = seriesMap.get(name);
    if (series) {
      series.posts.push(post);

      // 更新系列状态，使用最新文章的状态
      if (post.data.series.status) {
        series.status = post.data.series.status;
      }

      const postDate = new Date(post.data.modDate || post.data.pubDate);
      if (postDate > series.lastUpdated) {
        series.lastUpdated = postDate;
      }
    }
  }
});

// 排序系列文章
seriesMap.forEach(series => {
  // 排序文章
  series.posts.sort((a: CollectionEntry<'posts'>, b: CollectionEntry<'posts'>) => {
    if (!a.data.series || !b.data.series) return 0;
    return a.data.series.order - b.data.series.order;
  });
});

// 转换为数组
let seriesList = Array.from(seriesMap.values());

// 检查是否有自定义系列列表
if (carouselConfig.customSeries && carouselConfig.customSeries.length > 0) {
  // 过滤出自定义系列列表中的系列
  const customSeriesMap = new Map<string, Series>();

  // 按照自定义顺序排序
  carouselConfig.customSeries.forEach(seriesName => {
    const series = seriesList.find(s => s.name === seriesName);
    if (series) {
      customSeriesMap.set(seriesName, series);
    }
  });

  // 转换回数组，保持自定义顺序
  seriesList = carouselConfig.customSeries
    .filter(name => customSeriesMap.has(name))
    .map(name => {
      const series = customSeriesMap.get(name);
      // 这里我们已经通过filter确保了series存在
      return series as Series;
    })
    .filter(series => series.posts.length > 0); // 确保系列有文章

  // 如果sortBy为custom，则应用自定义排序
  if (carouselConfig.sortBy === 'custom' && carouselConfig.customSort) {
    switch (carouselConfig.customSort) {
      case 'asc': // 按文章数量升序
        seriesList.sort((a, b) => a.posts.length - b.posts.length);
        break;
      case 'desc': // 按文章数量降序
        seriesList.sort((a, b) => b.posts.length - a.posts.length);
        break;
      case 'alphabetical': // 按系列名称字母顺序
        seriesList.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'random': // 随机排序
        seriesList.sort(() => Math.random() - 0.5);
        break;
    }
  }
} else {
  // 根据配置排序系列
  if (carouselConfig.sortBy === 'latest') {
    // 按最后更新时间排序（最新的在前）
    seriesList.sort((a: Series, b: Series) => b.lastUpdated.getTime() - a.lastUpdated.getTime());
  } else if (carouselConfig.sortBy === 'articleCount') {
    // 按文章数量排序（多的在前）
    seriesList.sort((a: Series, b: Series) => b.posts.length - a.posts.length);
  } else if (carouselConfig.sortBy === 'custom' && carouselConfig.customSort) {
    // 应用自定义排序（即使没有customSeries）
    switch (carouselConfig.customSort) {
      case 'asc': // 按文章数量升序
        seriesList.sort((a, b) => a.posts.length - b.posts.length);
        break;
      case 'desc': // 按文章数量降序
        seriesList.sort((a, b) => b.posts.length - a.posts.length);
        break;
      case 'alphabetical': // 按系列名称字母顺序
        seriesList.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'random': // 随机排序
        seriesList.sort(() => Math.random() - 0.5);
        break;
    }
  }

  // 限制显示数量
  seriesList = seriesList.slice(0, maxItems);
}

// 获取状态的CSS类名
function getStatusClass(status: string): string {
  switch (status) {
    case '连载中':
      return 'ongoing';
    case '已完结':
      return 'completed';
    case '计划中':
      return 'planned';
    case '暂停更新':
      return 'paused';
    default:
      return 'ongoing';
  }
}
---

{seriesList.length > 0 && (
  <div class="series-carousel-container">
    <div class="carousel-wrapper">
      <button class="carousel-nav prev-button" aria-label="上一个系列">
        <span class="i-mdi-chevron-left"></span>
      </button>

      <div class="carousel-track">
        {seriesList.map((series, index) => (
          <div class="carousel-item" data-index={index}>
            <div class="series-card">
              <div class="series-header">
                <h3 class="series-title">
                  <a href={`/series/${getPathFromSeries(series.name, themeConfig.site.seriesMap)}/`}>
                    {series.name}
                  </a>
                </h3>
                <div class="series-meta">
                  <span class="series-count">{series.posts.length} 篇文章</span>
                  <span class={`series-status status-${getStatusClass(series.status)}`}>
                    {series.status}
                  </span>
                </div>
              </div>

              <ul class="series-articles">
                {series.posts.slice(0, showArticles).map((post: CollectionEntry<'posts'>) => (
                  <li class="article-item">
                    <a href={`/posts/${post.data.slug || post.id}/`} class="article-link">
                      <span class="article-order">{post.data.series?.order}.</span>
                      <span class="article-title">{post.data.title}</span>
                    </a>
                  </li>
                ))}
              </ul>

              <div class="series-footer">
                <a href={`/series/${getPathFromSeries(series.name, themeConfig.site.seriesMap)}/`} class="view-series-button">
                  查看全部
                  <span class="i-mdi-arrow-right"></span>
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>

      <button class="carousel-nav next-button" aria-label="下一个系列">
        <span class="i-mdi-chevron-right"></span>
      </button>
    </div>

    <div class="carousel-indicators">
      {seriesList.map((_, index) => (
        <button
          class={`indicator-dot ${index === 0 ? 'active' : ''}`}
          data-index={index}
          aria-label={`切换到第${index + 1}个系列`}
        ></button>
      ))}
    </div>
  </div>
)}

<script>
  // 初始化轮播功能
  function initCarousel() {
    const carouselTrack = document.querySelector('.carousel-track');
    const carouselItems = document.querySelectorAll('.carousel-item');
    const prevButton = document.querySelector('.prev-button');
    const nextButton = document.querySelector('.next-button');
    const indicators = document.querySelectorAll('.indicator-dot');

    if (!carouselTrack || !carouselItems.length || !prevButton || !nextButton) return;

    let currentIndex = 0;
    const itemCount = carouselItems.length;

    // 设置初始状态
    updateCarousel();

    // 上一个按钮点击事件
    prevButton.addEventListener('click', () => {
      currentIndex = (currentIndex - 1 + itemCount) % itemCount;
      updateCarousel();
    });

    // 下一个按钮点击事件
    nextButton.addEventListener('click', () => {
      currentIndex = (currentIndex + 1) % itemCount;
      updateCarousel();
    });

    // 指示器点击事件
    indicators.forEach(dot => {
      dot.addEventListener('click', () => {
        const index = parseInt(dot.getAttribute('data-index') || '0', 10);
        currentIndex = index;
        updateCarousel();
      });
    });

    // 触摸滑动支持
    let touchStartX = 0;
    let touchEndX = 0;

    carouselTrack.addEventListener('touchstart', (e) => {
      const touchEvent = e as TouchEvent;
      touchStartX = touchEvent.changedTouches[0].screenX;
    }, { passive: true });

    carouselTrack.addEventListener('touchend', (e) => {
      const touchEvent = e as TouchEvent;
      touchEndX = touchEvent.changedTouches[0].screenX;
      handleSwipe();
    }, { passive: true });

    function handleSwipe() {
      const swipeThreshold = 50; // 最小滑动距离
      if (touchEndX < touchStartX - swipeThreshold) {
        // 向左滑动，显示下一个
        currentIndex = (currentIndex + 1) % itemCount;
        updateCarousel();
      } else if (touchEndX > touchStartX + swipeThreshold) {
        // 向右滑动，显示上一个
        currentIndex = (currentIndex - 1 + itemCount) % itemCount;
        updateCarousel();
      }
    }

    // 自动轮播
    let autoplayInterval: number | undefined;
    const startAutoplay = () => {
      autoplayInterval = window.setInterval(() => {
        currentIndex = (currentIndex + 1) % itemCount;
        updateCarousel();
      }, 5000); // 5秒切换一次
    };

    const stopAutoplay = () => {
      if (autoplayInterval) {
        window.clearInterval(autoplayInterval);
      }
    };

    // 鼠标悬停时暂停自动轮播
    carouselTrack.addEventListener('mouseenter', stopAutoplay);
    carouselTrack.addEventListener('mouseleave', startAutoplay);

    // 触摸时暂停自动轮播
    carouselTrack.addEventListener('touchstart', stopAutoplay, { passive: true });
    carouselTrack.addEventListener('touchend', startAutoplay, { passive: true });

    // 更新轮播状态
    function updateCarousel() {
      // 更新轮播项位置
      carouselItems.forEach((item, index) => {
        const offset = index - currentIndex;
        (item as HTMLElement).style.transform = `translateX(${offset * 100}%)`;
        item.setAttribute('aria-hidden', String(index !== currentIndex));
      });

      // 更新指示器状态
      indicators.forEach((dot, index) => {
        dot.classList.toggle('active', index === currentIndex);
      });
    }

    // 开始自动轮播
    startAutoplay();
  }

  // 页面加载完成后初始化轮播
  document.addEventListener('DOMContentLoaded', initCarousel);
</script>
