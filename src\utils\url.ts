/**
 * URL相关的工具函数
 */
import type { Post } from '~/types'

/**
 * 将文本转换为URL友好的slug
 * @param text 需要转换的文本
 * @returns 处理后的slug
 */
export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/\s+/g, '-') // 将空格替换为连字符
    .replace(/[^\w\-]+/g, '') // 移除非单词字符（除了连字符）
    .replace(/\-\-+/g, '-') // 将多个连字符替换为单个连字符
    .replace(/^-+/, '') // 移除开头的连字符
    .replace(/-+$/, '') // 移除结尾的连字符
    .trim(); // 移除首尾空格
}

/**
 * 生成文章的URL
 * @param post 文章对象或文章ID
 * @param title 文章标题（可选，当post为ID时使用）
 * @returns 文章URL（始终以斜杠结尾）
 */
export function getPostUrl(post: Post | string, title?: string): string {
  let url = '';

  // 情况1: post是文章对象
  if (typeof post !== 'string') {
    // 优先使用自定义slug
    if (post.data.slug) {
      url = `/posts/${post.data.slug}`;
    } else {
      // 其次使用ID（文件名）
      url = `/posts/${post.id}`;
    }
  } else {
    // 情况2: post是ID字符串
    const id = post;
    // 如果提供了标题，则使用标题生成slug（这是为了兼容旧代码）
    if (title) {
      const slug = slugify(title);
      url = `/posts/${id}-${slug}`;
    } else {
      url = `/posts/${id}`;
    }
  }

  // 确保URL以斜杠结尾
  if (!url.endsWith('/')) {
    url += '/';
  }

  return url;
}

/**
 * 生成分类页面的URL
 * @param category 分类名称
 * @returns 分类页面URL（始终以斜杠结尾）
 */
export function getCategoryUrl(category: string): string {
  const url = `/categories/${slugify(category)}`;
  return url.endsWith('/') ? url : `${url}/`;
}

/**
 * 生成标签页面的URL
 * @param tag 标签名称
 * @returns 标签页面URL（始终以斜杠结尾）
 */
export function getTagUrl(tag: string): string {
  const url = `/tags/${slugify(tag)}`;
  return url.endsWith('/') ? url : `${url}/`;
}
