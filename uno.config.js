import presetAttributify from '@unocss/preset-attributify'
import transformerDirectives from '@unocss/transformer-directives'
import {
  defineConfig,
  presetIcons,
  presetTypography,
  presetWind3,
  transformerVariantGroup,
} from 'unocss'
import presetTheme from 'unocss-preset-theme'
import { themeConfig } from './src/.config'
import { breakpoints } from './src/utils/breakpoints'

const { colorsDark, colorsLight, fonts } = themeConfig.appearance

const cssExtend = {
  ':root': {
    '--prose-borders': '#eee',
  },

  'code::before,code::after': {
    content: 'none',
  },

  ':where(:not(pre):not(a) > code)': {
    'white-space': 'normal',
    'word-wrap': 'break-word',
    'padding': '2px 4px',
    'color': '#c7254e',
    'font-size': '90%',
    'background-color': '#f9f2f4',
    'border-radius': '4px',
  },

  'li': {
    'white-space': 'normal',
    'word-wrap': 'break-word',
  },
}

export default defineConfig({
  rules: [
    [
      /^row-(\d+)-(\d)$/,
      ([, start, end]) => ({ 'grid-row': `${start}/${end}` }),
    ],
    [
      /^col-(\d+)-(\d)$/,
      ([, start, end]) => ({ 'grid-column': `${start}/${end}` }),
    ],
    [
      /^scrollbar-hide$/,
      ([_]) => `.scrollbar-hide { scrollbar-width:none;-ms-overflow-style: none; }
      .scrollbar-hide::-webkit-scrollbar {display:none;}`,
    ],
  ],
  presets: [
    presetWind3(),
    presetTypography({ cssExtend }),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
      collections: {
        mdi: () => import('@iconify-json/mdi/icons.json').then(i => i.default),
      },
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    presetTheme ({
      theme: {
        dark: {
          colors: { ...colorsDark, shadow: '#FFFFFF0A' },
          // TODO 需要配置代码块颜色
        },
      },
    }),
  ],
  theme: {
    colors: { ...colorsLight, shadow: '#0000000A' },
    fontFamily: fonts,
    // 统一断点定义
    breakpoints: {
      xs: `${breakpoints.xs}px`,
      sm: `${breakpoints.sm}px`,
      md: `${breakpoints.md}px`,
      lg: `${breakpoints.lg}px`,
      xl: `${breakpoints.xl}px`,
    },
  },
  shortcuts: [
    ['post-title', 'text-5 font-bold lh-7.5 m-0'],
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  safelist: [
    // 复制和检查图标
    'i-mdi-content-copy',
    'i-mdi-check',

    // 社交媒体图标
    'i-mdi-twitter',
    'i-mdi-email-outline',
    'i-mdi-wechat',
    'i-mdi-github',
    'i-mdi-youtube',
    'i-mdi-facebook',
    'i-mdi-instagram',
    'i-mdi-linkedin',
    'i-mdi-telegram',
    'i-mdi-discord',
    'i-mdi-mastodon',
    'i-mdi-rss',

    // 导航和UI图标
    'i-mdi-folder-outline',
    'i-mdi-tag-outline',
    'i-mdi-account',
    'i-mdi-arrow-left',
    'i-mdi-arrow-right',
    'i-mdi-magnify', // 搜索图标
    'i-mdi-close', // 关闭图标
    'i-mdi-menu', // 菜单图标
    'i-mdi-home', // 主页图标
    'i-mdi-arrow-up', // 返回顶部图标
    'i-mdi-calendar', // 日历图标
    'i-mdi-clock-outline', // 时钟图标
    'i-mdi-eye-outline', // 查看图标
    'i-mdi-comment-outline', // 评论图标
    'i-mdi-share-variant', // 分享图标
    'i-mdi-archive-outline', // 归档图标
    'i-mdi-book-open-outline', // 系列图标
    'i-mdi-message-outline', // 留言板图标
    'i-mdi-information-outline', // 关于图标

    // 支付和赞赏图标
    'i-mdi-alpha-a-circle', // 支付宝图标
    'i-mdi-gift-outline', // 赞赏图标
    'i-mdi-currency-usd', // 美元图标
    'i-mdi-bitcoin', // 比特币图标
    'i-mdi-ethereum', // 以太坊图标

    // 主题和设置图标
    'i-mdi-theme-light-dark', // 主题切换图标
    'i-mdi-cog-outline', // 设置图标
    'i-mdi-translate', // 翻译图标

    // 文章和内容图标
    'i-mdi-file-document-outline', // 文档图标
    'i-mdi-image-outline', // 图片图标
    'i-mdi-link-variant', // 链接图标
    'i-mdi-bookmark-outline', // 书签图标
    'i-mdi-heart-outline', // 喜欢图标
    'i-mdi-star-outline', // 星标图标

    // 灯箱图标
    'i-mdi-close', // 关闭图标
    'i-mdi-chevron-left', // 左箭头图标
    'i-mdi-chevron-right', // 右箭头图标
    'i-mdi-plus', // 放大图标
    'i-mdi-minus', // 缩小图标
    'i-mdi-refresh', // 重置图标
  ],
})
