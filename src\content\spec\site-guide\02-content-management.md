---
title: "2. 内容管理"
pubDate: 2023-12-31
---

# 2. 内容管理

川流主题使用 Markdown 文件来管理内容，这使得创建和编辑文章变得简单直观。本章将介绍如何创建和管理文章、使用 Front-matter 字段、组织分类和标签，以及创建系列文章。

## 文章创建与编辑

### 创建新文章

在川流主题中，所有文章都存储在 `src/content/posts/` 目录下。创建新文章有两种方法：

#### 方法一：手动创建

1. 在 `src/content/posts/` 目录下创建一个新的 `.md` 文件
2. 文件名将成为文章的 URL 路径（例如，`my-first-post.md` 将对应 `/posts/my-first-post/`）
3. 添加必要的 Front-matter 和文章内容

#### 方法二：使用模板

1. 复制 `src/content/templates/post-template.md` 或 `post-template-simple.md` 到 `src/content/posts/` 目录
2. 重命名文件为您想要的 URL 路径
3. 编辑 Front-matter 和文章内容

#### 方法三：使用命令（如果配置了此功能）

```bash
npm run new-post
```

这个命令会引导您输入文章标题、分类等信息，然后自动创建文章文件。

### 编辑文章

您可以使用任何文本编辑器或 Markdown 编辑器来编辑文章。推荐使用：

- **VS Code**：搭配 Markdown 插件
- **Typora**：所见即所得的 Markdown 编辑器
- **Obsidian**：功能强大的知识管理工具

## Front-matter 字段详解

Front-matter 是文章开头的 YAML 格式元数据，位于三个连字符 `---` 之间。川流主题支持多种 Front-matter 字段：

### 必填字段

```yaml
---
title: "文章标题"                      # 文章标题（必填）
pubDate: 2023-11-15                   # 发布日期（必填，格式：YYYY-MM-DD）
categories: ["分类1", "分类2"]         # 文章分类（必填，至少一个分类）
---
```

### 常用可选字段

```yaml
---
description: "文章的简短描述，用于摘要和SEO" # 文章描述（推荐填写，利于SEO）
tags: ["标签1", "标签2", "标签3"]      # 文章标签（可选）
author: "作者名称"                     # 文章作者（可选，默认使用站点配置中的作者）
modDate: 2023-11-20                   # 最后修改日期（可选，格式同pubDate）
draft: false                          # 是否为草稿（可选，true=草稿，生产环境不显示）
banner: ./images/banner.jpg           # 文章banner图片（可选，路径相对于文章所在目录）
---
```

### 特殊功能字段

```yaml
---
pin: false                            # 是否置顶（可选，true=置顶）
pinOrder: 0                           # 置顶顺序（可选，数字越小越靠前，默认为0）
slug: "custom-article-url"            # 自定义URL路径（可选，影响取决于路由配置）
disableAds: false                     # 是否禁用所有广告（可选，true=禁用）
---
```

### 系列文章字段

```yaml
---
series:
  name: "系列名称"                     # 系列名称
  order: 1                            # 在系列中的顺序
---
```

## Markdown 语法指南

川流主题支持标准 Markdown 语法，以及一些扩展功能：

### 基本语法

```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文本** 和 *斜体文本*

- 无序列表项
- 另一个列表项

1. 有序列表项
2. 另一个有序列表项

[链接文本](https://example.com)

![图片描述](./images/example.jpg)

> 这是一段引用文本
```

### 代码块

````markdown
```javascript
// 代码示例
function hello() {
  console.log("Hello, world!");
}
```
````

### 表格

```markdown
| 表头1 | 表头2 | 表头3 |
|-------|-------|-------|
| 单元格1 | 单元格2 | 单元格3 |
| 单元格4 | 单元格5 | 单元格6 |
```

### 任务列表

```markdown
- [x] 已完成任务
- [ ] 未完成任务
```

## 分类与标签管理

川流主题支持文章分类和标签，帮助您组织内容并提高可发现性。

### 分类系统

分类是对文章的主要分组，每篇文章必须至少属于一个分类。分类通常代表文章的主题领域，如"技术"、"生活"等。

在 `src/.config/user.ts` 中配置分类映射：

```typescript
categoryMap: [
  { name: '技术', path: 'technology' },
  { name: '生活', path: 'life' },
  { name: '思考', path: 'thoughts' },
  // 可以添加更多分类...
],
```

这个映射将中文分类名转换为英文 URL 路径，有利于 SEO。

### 标签系统

标签是对文章内容的更细粒度描述，一篇文章可以有多个标签或没有标签。标签通常代表文章中提到的具体技术、工具或概念。

在 `src/.config/user.ts` 中配置标签映射：

```typescript
tagMap: [
  { name: '编程', path: 'programming' },
  { name: '旅行', path: 'travel' },
  { name: '阅读', path: 'reading' },
  // 可以添加更多标签...
],
```

### 最佳实践

1. **保持一致性**：使用一致的分类和标签命名
2. **避免过度分类**：分类数量应该保持在合理范围内（通常不超过10个）
3. **标签要具体**：标签应该具体且有意义，避免过于宽泛的标签
4. **更新映射表**：当添加新的中文分类或标签时，记得更新映射表

## 系列文章创建

系列文章是一组相关联的文章，按照特定顺序排列。川流主题提供了完善的系列文章功能。

### 创建系列文章

1. 在每篇属于同一系列的文章中添加系列信息：

```yaml
---
series:
  name: "Astro系列教程"  # 系列名称（必须完全相同）
  order: 1              # 在系列中的顺序（从1开始）
---
```

2. 确保同一系列的所有文章使用完全相同的系列名称（包括大小写和空格）
3. 使用 `order` 字段指定文章在系列中的顺序

### 系列文章导航

系统会自动在系列文章页面添加系列导航组件，显示：

- 系列中的所有文章列表
- 当前文章在系列中的位置
- 上一篇和下一篇文章的链接

### 系列文章页面

所有系列都会在 `/series` 页面中列出，用户可以浏览所有系列。

### 系列文章最佳实践

1. **提前规划**：在开始写系列文章前，先规划好整个系列的结构和内容
2. **保持连贯性**：确保系列文章之间有清晰的逻辑连接
3. **内部链接**：在系列文章中添加指向系列中其他文章的链接
4. **统一风格**：保持系列文章的风格一致性
5. **合理分段**：将长内容分成多篇文章，每篇文章聚焦于一个主题

## 图片和媒体管理

### 文章图片

有两种方式管理文章中的图片：

1. **相对路径**：将图片放在与文章相同的目录或子目录中，使用相对路径引用
   ```markdown
   ![图片描述](./images/example.jpg)
   ```

2. **公共目录**：将图片放在 `public/images/` 目录中，使用绝对路径引用
   ```markdown
   ![图片描述](/images/example.jpg)
   ```

### 文章 Banner

文章 Banner 是显示在文章顶部的大图，在 Front-matter 中设置：

```yaml
---
banner: ./images/banner.jpg  # 路径相对于文章所在目录
---
```

### 图片优化建议

1. **压缩图片**：使用工具如 [TinyPNG](https://tinypng.com/) 压缩图片
2. **合适的尺寸**：避免使用过大的图片，建议宽度不超过1200px
3. **响应式图片**：考虑提供多种尺寸的图片
4. **替代文本**：始终为图片提供有意义的替代文本，有利于SEO和无障碍访问

## 下一步

现在您已经了解了如何管理内容，接下来可以：

1. [自定义网站外观](./03-theme-customization.md)
2. [配置功能模块](./04-features-configuration.md)
3. [优化SEO](./05-seo-optimization.md)
