---
import type { GetStaticPaths, InferGetStaticPropsType } from 'astro'
import Comments from '~/components/Comments.astro'
import Donate from '~/components/Donate.astro'
import Pagination from '~/components/Pagination.astro'
import SiteSeo from '~/components/SiteSeo.astro'
import SocialShare from '~/components/SocialShare.astro'
import SeriesNavigation from '~/components/SeriesNavigation.astro'
import RelatedPosts from '~/components/RelatedPosts.astro'
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import LayoutPost from '~/layouts/LayoutPost.astro'
import { getPosts } from '~/utils'
import { render } from 'astro:content'

export const getStaticPaths = (async () => {
  const posts = await getPosts()
  return posts.map((post: any, idx: number) => {
    const prev = posts[idx - 1]
    const next = posts[idx + 1]

    // 使用自定义slug或文件ID作为URL参数
    const urlParam = post.data.slug || post.id

    return {
      params: { id: urlParam },
      props: { entry: post, next, prev },
    }
  })
}) satisfies GetStaticPaths

// type Params = InferGetStaticParamsType<typeof getStaticPaths>
type Props = InferGetStaticPropsType<typeof getStaticPaths>

const { entry, prev, next } = Astro.props
const { Content } = await render(entry)

// 组件
const components = {}
const { translate: t } = Astro.locals
---

<LayoutDefault>
  <SiteSeo slot="seo" title={entry.data.title} desc={entry.data.description} banner={entry.data.banner} />

  <div class="tablet-centered">
    <LayoutPost post={entry}>
      <!-- 1. 文章正文 -->
      <Content components={components} />

      <!-- 2. 系列文章目录（如果是系列文章则显示） -->
      {entry.data.series && <SeriesNavigation currentPost={entry} />}

      <!-- 3. 赞赏按钮 -->
      <Donate />

      <!-- 4. 社交分享 -->
      <SocialShare
        url={`${Astro.site}posts/${entry.data.slug || entry.id}/`}
        title={entry.data.title}
        description={entry.data.description || ''}
      />

      <!-- 5. 上一篇/下一篇导航 -->
      <div class="post-navigation no-margin">
        <Pagination
          showLeft={Boolean(prev)}
          leftTitle={`${t('prev_post')}: ${prev?.data.title}`}
          leftUrl={prev ? (prev.data.slug ? `/posts/${prev.data.slug}/` : `/posts/${prev.id}/`) : undefined}
          showRight={Boolean(next)}
          rightTitle={`${t('next_post')}: ${next?.data.title}`}
          rightUrl={next ? (next.data.slug ? `/posts/${next.data.slug}/` : `/posts/${next.id}/`) : undefined}
          showPageCount={false}
        />
      </div>

      <!-- 6. 相关文章推荐（非系列文章才显示） -->
      {!entry.data.series && <RelatedPosts currentPost={entry} />}

      <!-- 7. 评论区 -->
      <!-- BUG 这里使用 client:visible 会导致构建失败。ref: https://github.com/withastro/astro/issues/7329 -->
      <Comments post={entry} />
    </LayoutPost>
  </div>
</LayoutDefault>

<style is:global>
  /* 文章导航样式 */
  .post-navigation {
    margin: 0 auto;
    padding: 0;
    border-top: none;
    width: 100%;
    max-width: 100%;
    text-align: center;
  }

  .dark .post-navigation {
    border-color: #333;
  }

  .dark .post-navigation .pagination-item {
    border-color: rgba(255, 255, 255, 0.2); /* 暗色模式下的边框颜色 */
  }

  /* 文章详情页的导航按钮特殊样式 */
  .post-navigation .pagination {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 0.25rem;
    margin: 0;
    padding: 0;
  }

  .post-navigation .pagination-info {
    width: 100%;
  }

  .post-navigation .pagination-item {
    min-width: 200px; /* 增加最小宽度 */
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.5rem 1rem; /* 增加内边距 */
    height: auto;
    margin: 0;
    border-radius: 0.5rem; /* 增加圆角 */
    border: 1px solid rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.2); /* 添加边框 */
  }

  /* 文章详情页占位元素样式 */
  .post-navigation .pagination-spacer-left,
  .post-navigation .pagination-spacer-right {
    flex: 1;
    min-width: 200px; /* 与按钮保持一致的最小宽度 */
  }

  .post-navigation .pagination-prev {
    justify-content: flex-start;
    text-align: left;
  }

  .post-navigation .pagination-next {
    justify-content: flex-end;
    text-align: right;
  }

  .post-navigation .pagination-icon {
    margin: 0;
    flex-shrink: 0;
  }

  .post-navigation .pagination-prev .pagination-icon {
    margin-right: 0.25rem;
  }

  .post-navigation .pagination-next .pagination-icon {
    margin-left: 0.25rem;
  }

  .post-navigation .pagination-text {
    white-space: normal;
    line-height: 1.4;
    max-width: none;
    overflow: visible;
    margin: 0;
    word-break: break-word;
    font-size: 0.95rem; /* 调整字体大小 */
    font-weight: 500; /* 增加字体粗细 */
  }

  @media (max-width: 640px) {
    .post-navigation .pagination {
      flex-direction: row;
      gap: 0.125rem;
      margin: 0;
      padding: 0;
    }

    .post-navigation .pagination-item {
      padding: 0.375rem 0.75rem; /* 调整内边距 */
      min-height: auto;
      height: auto;
      min-width: 140px; /* 移动端上的最小宽度 */
      max-width: none; /* 移除最大宽度限制 */
      border: 1px solid rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.2); /* 保持边框样式 */
    }

    .post-navigation .pagination-text {
      font-size: 0.8rem;
    }

    .post-navigation .pagination-icon {
      width: 1rem;
      height: 1rem;
    }

    /* 移动端占位元素样式 */
    .post-navigation .pagination-spacer-left,
    .post-navigation .pagination-spacer-right {
      min-width: 140px; /* 与按钮保持一致的最小宽度 */
    }
  }
</style>
