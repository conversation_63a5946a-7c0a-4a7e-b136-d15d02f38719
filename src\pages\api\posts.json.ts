import type { APIRoute } from 'astro';
import { getPosts, getPostDescription } from '~/utils';

export const GET: APIRoute = async () => {
  try {
    // 获取所有文章
    const posts = await getPosts();

    // 转换为简化的JSON格式
    const simplifiedPosts = posts.map(post => ({
      id: post.id,
      slug: post.data.slug,
      title: post.data.title,
      pubDate: post.data.pubDate,
      pin: post.data.pin === true,
      pinOrder: post.data.pinOrder || 0,
      description: post.data.description || '',
      // 添加预处理好的摘要字段，使用与首页相同的逻辑
      excerpt: getPostDescription(post),
      categories: post.data.categories || [],
      tags: post.data.tags || []
    }));

    // 返回JSON响应
    return new Response(JSON.stringify(simplifiedPosts), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error generating posts JSON:', error);
    return new Response(JSON.stringify({ error: 'Failed to generate posts data' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}
