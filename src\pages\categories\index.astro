---
import { themeConfig } from '~/.config'
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import { getCategories, getPathFromCategory } from '~/utils/index'

const { translate: t } = Astro.locals
const categoryMap = await getCategories()

const list = getListFromMap(categoryMap)

function getListFromMap(map: typeof categoryMap) {
  return Array.from(map).map(([key, value]) => ({
    title: key,
    href: `/categories/${getPathFromCategory(key, themeConfig.site.categoryMap)}`,
    size: t('categories_count', value.length),
  }))
}
---

<LayoutDefault>
  <div class="categories-container">
    <h1 class="page-title">{t('Categories')}</h1>
    <p class="page-description">浏览所有文章分类</p>

    <div class="categories-grid">
      {
        list.map(({ title, href, size }) => (
          <a href={href} class="category-card not-underline-hover">
            <div class="category-icon">
              <span class="i-mdi-folder-outline"></span>
            </div>
            <div class="category-info">
              <h3 class="category-title">{title}</h3>
              <p class="category-count">{size}</p>
            </div>
          </a>
        ))
      }
    </div>
  </div>
</LayoutDefault>

<style>
  /* 分类页容器 */
  .categories-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* 页面标题和描述 - 使用全局 .page-title 样式 */
  .page-title {
    margin-bottom: 0.75rem;
  }

  .page-description {
    margin-bottom: 1.5rem;
    color: #666;
    font-size: 1rem;
  }

  .dark .page-description {
    color: #aaa;
  }

  /* 分类网格 */
  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }

  /* 分类卡片 - 更紧凑的设计 */
  .category-card {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    background-color: #f5f5f5;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  }

  .dark .category-card {
    background-color: #2a2a2a;
    border-color: rgba(255, 255, 255, 0.05);
  }

  .dark .category-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* 分类图标 */
  .category-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 6px;
    background-color: rgba(46, 64, 91, 0.1);
    margin-right: 0.75rem;
    flex-shrink: 0;
  }

  .category-icon span {
    font-size: 1.25rem;
    color: var(--tw-color-primary, #2e405b);
  }

  .dark .category-icon {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .dark .category-icon span {
    color: #e5e7eb;
  }

  /* 分类信息 */
  .category-info {
    flex: 1;
    min-width: 0; /* 防止文本溢出 */
  }

  .category-title {
    font-size: 1rem;
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .category-count {
    font-size: 0.8rem;
    margin: 0;
    color: #666;
  }

  .dark .category-count {
    color: #aaa;
  }

  /* 响应式设计 - 平板和移动端 */
  @media (max-width: 1023px) {
    .categories-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }

    /* 移除对 .page-title 字号的覆盖 */
  }

  @media (max-width: 768px) {
    .categories-grid {
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
      gap: 0.75rem;
    }

    .category-card {
      padding: 0.625rem 0.875rem;
    }

    .category-icon {
      width: 32px;
      height: 32px;
      margin-right: 0.625rem;
    }

    .category-icon span {
      font-size: 1.125rem;
    }

    .category-title {
      font-size: 0.9375rem;
    }

    .category-count {
      font-size: 0.75rem;
    }

    .page-title {
      margin-bottom: 0.5rem;
    }

    .page-description {
      font-size: 0.9375rem;
      margin-bottom: 1.25rem;
    }
  }

  @media (max-width: 480px) {
    .categories-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem; /* 增加间距 */
    }

    .category-card {
      padding: 0.75rem 1rem; /* 增加内边距 */
    }

    .category-icon {
      width: 32px; /* 增加图标尺寸 */
      height: 32px;
      margin-right: 0.625rem;
    }

    .category-icon span {
      font-size: 1.125rem; /* 增加图标字体大小 */
    }

    .category-title {
      font-size: 0.95rem; /* 增加标题字体大小 */
    }

    .category-count {
      font-size: 0.75rem; /* 略微增加计数字体大小 */
    }
  }
</style>
