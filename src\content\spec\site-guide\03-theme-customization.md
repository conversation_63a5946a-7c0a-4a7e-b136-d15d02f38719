---
title: "3. 主题定制"
pubDate: 2023-12-31
---

# 3. 主题定制

川流主题提供了丰富的自定义选项，让您可以根据个人喜好调整网站的外观和行为。本章将介绍如何设置网站基本信息、自定义颜色和样式、配置导航菜单等。

## 配置文件概述

川流主题的配置系统分为两部分：

1. **默认配置**：位于 `src/.config/default.ts`，包含主题的默认设置
2. **用户配置**：位于 `src/.config/user.ts`，用于覆盖默认设置

您只需要修改 `user.ts` 文件，不建议直接修改 `default.ts`。

配置文件使用 TypeScript 格式，但即使您不熟悉 TypeScript，也可以轻松修改配置。

## 网站基本信息设置

在 `src/.config/user.ts` 中，`site` 部分包含网站的基本信息：

```typescript
site: {
  title: '川流',                // 网站标题
  subtitle: '一切为了自由',      // 网站副标题
  author: '番茄',              // 作者名称
  description: '川流,自由,信念,理想,Flow, freedom, belief, ideal', // 网站描述
  website: 'https://example.com/', // 网站URL（重要！）
  pageSize: 5,                 // 每页显示的文章数量
  // 其他设置...
},
```

### 重要字段说明

- **title**：显示在浏览器标签页和网站头部的主标题
- **subtitle**：显示在网站头部的副标题
- **author**：文章的默认作者名称
- **description**：用于SEO的网站描述，应包含关键词
- **website**：您的网站URL，影响站点地图和其他功能
- **pageSize**：首页和分类页面每页显示的文章数量

## 社交链接配置

在 `site` 部分中，`socialLinks` 数组用于配置显示在网站页脚的社交媒体链接：

```typescript
socialLinks: [
  {
    name: 'github',
    href: 'https://github.com/yourusername',
  },
  {
    name: 'twitter',
    href: 'https://twitter.com/yourusername',
  },
  {
    name: 'telegram',
    href: 'https://t.me/yourusername',
  },
  {
    name: 'email',
    href: 'mailto:<EMAIL>',
  },
  {
    name: 'link',
    href: 'https://example.com',
    title: '自定义链接', // 自定义显示名称
  },
  // 可以添加更多社交链接...
],
```

### 支持的社交媒体类型

川流主题内置支持多种社交媒体图标，包括：

- github
- twitter
- facebook
- instagram
- linkedin
- youtube
- telegram
- email
- rss
- link（通用链接，可自定义标题）

## 导航菜单配置

在 `site` 部分中，`navLinks` 数组用于配置网站顶部的导航菜单：

```typescript
navLinks: [
  {
    name: '首页',
    href: '/',
  },
  {
    name: '归档',
    href: '/archive',
  },
  {
    name: '分类',
    href: '/categories',
  },
  {
    name: '标签',
    href: '/tags',
  },
  {
    name: '系列',
    href: '/series',
  },
  {
    name: '友链',
    href: '/links',
  },
  {
    name: '留言板',
    href: '/guestbook',
  },
  {
    name: '搜索',
    href: '/search',
  },
  {
    name: '关于',
    href: '/about',
  },
  // 可以添加更多导航链接...
],
```

### 自定义导航链接

您可以根据需要添加、删除或重新排序导航链接。每个链接包含两个属性：

- **name**：显示在导航菜单中的名称
- **href**：链接的目标URL（相对于网站根目录）

### 添加外部链接

您也可以添加指向外部网站的链接：

```typescript
{
  name: '我的GitHub',
  href: 'https://github.com/yourusername',
},
```

## 页脚配置

在 `site` 部分中，`footer` 数组用于配置网站页脚的文本：

```typescript
footer: [
  '© %year <a target="_blank" href="%website">%author</a>', // 第一行
  '一切为了自由', // 第二行
  // 可以添加更多行...
],
```

### 特殊占位符

页脚文本支持以下占位符：

- **%year**：当前年份
- **%website**：网站URL（来自site.website）
- **%author**：作者名称（来自site.author）

## 颜色和样式自定义

在 `appearance` 部分，您可以自定义网站的颜色和主题：

```typescript
appearance: {
  theme: 'system', // 'light' | 'dark' | 'system'
  locale: 'zh-cn',
  colorsLight: {
    primary: '#2e405b',   // 主色调（亮色模式）
    background: '#ffffff', // 背景色（亮色模式）
  },
  colorsDark: {
    primary: '#FFFFFF',   // 主色调（暗色模式）
    background: '#232222', // 背景色（暗色模式）
  },
  fonts: {
    header: '"HiraMinProN-W6","Source Han Serif CN","Source Han Serif SC","Source Han Serif TC",serif', // 标题字体
    ui: '"Source Sans Pro","Roboto","Helvetica","Helvetica Neue","Source Han Sans SC","Source Han Sans TC","PingFang SC","PingFang HK","PingFang TC",sans-serif', // 界面字体
  },
},
```

### 主题模式

`theme` 字段控制网站的默认主题模式：

- **light**：始终使用亮色模式
- **dark**：始终使用暗色模式
- **system**：根据用户系统设置自动切换（推荐）

### 自定义颜色

您可以修改 `colorsLight` 和 `colorsDark` 中的颜色值来自定义网站的配色方案：

- **primary**：主色调，用于链接、按钮等元素
- **background**：网站背景色

颜色值可以使用十六进制格式（如 `#2e405b`）或 CSS 颜色名称（如 `blue`）。

### 自定义字体

您可以修改 `fonts` 中的字体设置来自定义网站的字体：

- **header**：用于标题的字体
- **ui**：用于界面元素的字体

字体设置使用 CSS `font-family` 格式，可以指定多个字体作为备选。

## 首页布局调整

川流主题的首页布局主要由以下部分组成：

1. **头部**：显示网站标题、副标题和导航菜单
2. **文章列表**：显示最新的文章
3. **侧边栏**：显示分类、标签云等小部件
4. **页脚**：显示版权信息和社交链接

### 调整每页显示的文章数量

在 `site` 部分中，修改 `pageSize` 字段：

```typescript
pageSize: 5, // 每页显示5篇文章
```

### 自定义首页标题

如果您想自定义首页的标题和描述，可以编辑 `src/pages/index.astro` 文件。

## 分类和标签映射

在 `site` 部分中，`categoryMap` 和 `tagMap` 数组用于将中文分类和标签名映射为英文URL：

```typescript
// 分类映射表
categoryMap: [
  { name: '技术', path: 'technology' },
  { name: '生活', path: 'life' },
  { name: '思考', path: 'thoughts' },
  // 可以添加更多映射...
],

// 标签映射表
tagMap: [
  { name: '编程', path: 'programming' },
  { name: '旅行', path: 'travel' },
  { name: '阅读', path: 'reading' },
  // 可以添加更多映射...
],
```

这些映射表有两个作用：

1. 将中文分类/标签名转换为对SEO更友好的英文URL
2. 确保分类/标签名的一致性

## 国际化设置

在 `appearance` 部分，`locale` 字段用于设置网站的语言：

```typescript
locale: 'zh-cn', // 简体中文
```

川流主题支持多种语言，包括：

- `zh-cn`：简体中文
- `zh-tw`：繁体中文
- `en-us`：英文（美国）
- `ja-jp`：日文
- `it-it`：意大利文

## 高级样式自定义

如果您需要更深入的样式自定义，可以考虑以下方法：

### 1. 使用自定义CSS

创建 `src/styles/custom.css` 文件，添加您的自定义样式，然后在 `src/layouts/LayoutDefault.astro` 中导入。

### 2. 修改UnoCSS配置

川流主题使用 UnoCSS 进行样式管理，您可以修改 `uno.config.js` 文件来自定义样式规则。

### 3. 覆盖组件样式

您可以复制 `src/components/` 中的组件到您的项目中，然后修改它们的样式。

## 下一步

现在您已经了解了如何自定义主题外观，接下来可以：

1. [配置功能模块](./04-features-configuration.md)
2. [优化SEO](./05-seo-optimization.md)
3. [进行高级自定义](./06-advanced-customization.md)
