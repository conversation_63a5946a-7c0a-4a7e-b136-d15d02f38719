---
import { themeConfig } from '~/.config'
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import LayoutPost from '~/layouts/LayoutPost.astro'
import SiteSeo from '~/components/SiteSeo.astro'
import SeriesCarousel from '~/components/SeriesCarousel.astro'
import { getPostDescription, getPosts } from '~/utils'

// 导入轮播样式
import '~/styles/carousel.css'

const { translate: t } = Astro.locals

// 检查是否启用轮播
const carouselEnabled = themeConfig.carousel?.enable !== false

// 检查设备显示设置
const deviceDisplay = themeConfig.carousel?.deviceDisplay || { desktop: true, tablet: true, mobile: true }

// 根据设备类型决定是否显示轮播
const showCarousel = carouselEnabled

// 获取所有文章
const allPosts = await getPosts()

// 分离置顶文章和非置顶文章
const pinnedPosts = allPosts.filter(post => post.data.pin === true)
  .sort((a, b) => (a.data.pinOrder || 0) - (b.data.pinOrder || 0))

const regularPosts = allPosts.filter(post => post.data.pin !== true)

// 计算需要显示的非置顶文章数量
const regularPostsToShow = Math.max(0, 6 - pinnedPosts.length)

// 合并置顶文章和最新的非置顶文章，总数不超过6篇
const displayPosts = [
  ...pinnedPosts,
  ...regularPosts.slice(0, regularPostsToShow)
]

// 计算是否还有更多文章可以加载
const hasMorePosts = regularPosts.length > regularPostsToShow

// 为结构化数据准备文章列表项
const itemListElements = displayPosts.map((post, index) => ({
  "@type": "ListItem",
  "position": index + 1,
  "url": new URL(`posts/${post.data.slug || post.id}/`, Astro.site).toString(),
  "name": post.data.title
}));
---

<LayoutDefault>
  <SiteSeo slot="seo" />

  <!-- 首页结构化数据 -->
  <script is:inline type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "headline": `${themeConfig.site.title} - ${themeConfig.site.subtitle}`,
    "description": themeConfig.site.description,
    "url": themeConfig.site.website,
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": itemListElements
    }
  })} />

  <div class="container tablet-centered">
    {/* 系列文章轮播 */}
    {showCarousel && (
      <div class={`carousel-container
        ${deviceDisplay.desktop ? '' : 'hide-on-desktop'}
        ${deviceDisplay.tablet ? '' : 'hide-on-tablet'}
        ${deviceDisplay.mobile ? '' : 'hide-on-mobile'}`}>
        <SeriesCarousel />
      </div>
    )}

    <section class="post-list">
      {
        displayPosts.map((post) => (
          <>
            <LayoutPost post={post} isListItem={true}>
              <p class="line-clamp-4">{getPostDescription(post)}</p>
            </LayoutPost>
          </>
        ))
      }
    </section>

    {/* 加载更多文章按钮 */}
    {hasMorePosts && (
      <div class="view-all-container">
        <button id="load-more-button" class="load-more-button">
          <span class="i-mdi-refresh load-more-icon"></span>
          {t('load_more_posts') || '加载更多文章'}
        </button>
      </div>
    )}

    {/* 查看所有文章按钮 */}
    <div class="view-all-container">
      <a href="/posts/" class="view-all-button">
        <span class="i-mdi-format-list-bulleted view-all-icon"></span>
        {t('view_all_posts') || '查看所有文章'}
      </a>
    </div>
  </div>
</LayoutDefault>

<script>
  // 加载更多文章功能
  document.addEventListener('DOMContentLoaded', function() {
    const loadMoreButton = document.getElementById('load-more-button') as HTMLButtonElement | null;
    const postList = document.querySelector('.post-list');

    if (!loadMoreButton || !postList) return;

    // 当前已加载的文章数量（初始为显示的文章数量）
    let loadedPostsCount = document.querySelectorAll('.post-list > *').length / 2; // 除以2是因为每个文章有两个元素（文章本身和空的Fragment）

    // 每次加载的文章数量
    const postsPerLoad = 6;

    // 所有文章的总数
    const allRegularPosts: any[] = [];

    // 获取所有文章数据
    async function fetchAllPosts() {
      try {
        const response = await fetch('/api/posts.json');
        if (!response.ok) {
          throw new Error('Failed to fetch posts');
        }
        const data = await response.json();

        // 过滤出非置顶文章
        const regularPosts = data.filter((post: any) => !post.pin);
        allRegularPosts.push(...regularPosts);

        // 检查是否还有更多文章可以加载
        checkMorePosts();
      } catch (error) {
        console.error('Error fetching posts:', error);
        if (loadMoreButton) {
          loadMoreButton.style.display = 'none';
        }
      }
    }

    // 检查是否还有更多文章可以加载
    function checkMorePosts() {
      if (loadMoreButton && loadedPostsCount >= allRegularPosts.length) {
        loadMoreButton.style.display = 'none';
      }
    }

    // 加载更多文章
    async function loadMorePosts() {
      if (!loadMoreButton || !postList) return;

      // 计算需要加载的文章范围
      const startIndex = loadedPostsCount;
      const endIndex = Math.min(startIndex + postsPerLoad, allRegularPosts.length);

      if (startIndex >= allRegularPosts.length) {
        loadMoreButton.style.display = 'none';
        return;
      }

      // 显示加载中状态
      loadMoreButton.disabled = true;
      loadMoreButton.innerHTML = '<span class="i-mdi-loading load-more-icon loading"></span> 加载中...';

      try {
        // 获取要加载的文章
        const postsToLoad = allRegularPosts.slice(startIndex, endIndex);

        // 创建文章元素并添加到列表中
        for (const post of postsToLoad) {
          // 创建文章容器
          const articleContainer = document.createElement('div');
          articleContainer.className = 'article-container';

          // 设置文章HTML
          articleContainer.innerHTML = `
            <article class="prose">
              <div class="post-meta">
                <h2 class="post-title">
                  <a href="/posts/${post.slug || post.id}/">${post.title}</a>
                </h2>
                <div class="post-info">
                  <span class="post-date">${new Date(post.pubDate).toLocaleDateString()}</span>
                </div>
              </div>
              <p class="line-clamp-4">${post.excerpt || post.description || ''}</p>
            </article>
          `;

          // 添加到文章列表
          postList.appendChild(articleContainer);
        }

        // 更新已加载的文章数量
        loadedPostsCount += postsToLoad.length;

        // 检查是否还有更多文章
        checkMorePosts();
      } catch (error) {
        console.error('Error loading more posts:', error);
      } finally {
        if (loadMoreButton) {
          // 恢复按钮状态
          loadMoreButton.disabled = false;
          loadMoreButton.innerHTML = `<span class="i-mdi-refresh load-more-icon"></span> ${document.documentElement.lang === 'zh-cn' ? '加载更多文章' : 'Load More Posts'}`;
        }
      }
    }

    // 绑定加载更多按钮点击事件
    loadMoreButton.addEventListener('click', loadMorePosts);

    // 初始化
    fetchAllPosts();
  });
</script>

<style>
  .view-all-container {
    display: flex;
    justify-content: center;
    margin: 1rem 0 1.5rem;
  }

  .view-all-button, .load-more-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--uno-colors-primary, #2e405b);
    color: white;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
  }

  .view-all-button:hover, .load-more-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .view-all-icon, .load-more-icon {
    font-size: 1.2rem;
  }

  /* 加载更多按钮特有样式 */
  .load-more-button {
    background-color: #4caf50; /* 绿色，区别于查看所有文章按钮 */
  }

  /* 加载中动画 */
  .loading {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 暗色模式适配 */
  :global(.dark) .view-all-button {
    background-color: var(--uno-colors-primary, #FFFFFF);
    color: var(--uno-colors-background, #232222);
  }

  :global(.dark) .load-more-button {
    background-color: #2e7d32; /* 深绿色，适合暗色模式 */
    color: white;
  }
</style>
