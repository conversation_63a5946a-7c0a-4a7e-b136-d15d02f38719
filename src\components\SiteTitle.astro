---
import { themeConfig } from '~/.config'

const { title, subtitle } = themeConfig.site
---

<hgroup
  lg="write-vertical-right items-start b-l-2px b-l-primary-solid text-left"
  flex="~ col gap-2.5"
  class="cursor-pointer text-center duration-800 ease-in-out"
>
  <a
    class="not-underline-hover duration-800 ease-in-out"
    lg:p="x-2.5 b-12 hover:(t-3.75 b-8.75)"
    hover:lg=" bg-primary color-background"
    href="/"
  >
    <h3 class="text-5 font-extrabold font-header subtitle">{subtitle}</h3>
    <h1 class="text-8 font-extrabold font-header">{title}</h1>
  </a>
</hgroup>

<style>
  /* 修复左侧边框颜色 */
  @media (min-width: 1024px) {
    hgroup {
      border-left: 2px solid #2e405b !important;
    }

    .dark hgroup {
      border-left: 2px solid #ffffff !important;
    }
  }
</style>
