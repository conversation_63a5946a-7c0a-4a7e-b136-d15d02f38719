---
import { themeConfig } from '~/.config'

const year = new Date().getFullYear()

const { website, author, footer } = themeConfig.site

function parseFooter(input: string) {
  let result = input.replace(/%author/g, author)
  result = result.replace(/%website/g, website)
  result = result.replace(/%year/g, year.toString())
  return result
}
---

<div class="footer-container">
  {footer.length && footer.map((str) => <p set:html={parseFooter(str)} />)}
</div>

<style>
  .footer-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  p {
    --at-apply: 'font-bold text-3';
    text-align: left;
    margin: 0;
    line-height: 1.5;
    transition: color 0.3s ease;
  }

  p a {
    color: var(--uno-colors-primary, #2e405b);
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid transparent;
  }

  p a:hover {
    border-bottom-color: var(--uno-colors-primary, #2e405b);
  }

  .dark p a {
    color: #e5e7eb;
  }

  .dark p a:hover {
    border-bottom-color: #e5e7eb;
  }

  /* 移动端和平板模式居中显示 */
  @media (max-width: 1023px) {
    .footer-container {
      align-items: center;
    }

    p {
      text-align: center;
    }
  }
</style>
