/**
 * 系列文章轮播样式
 */

/* 轮播容器 */
.series-carousel-container {
  margin: 0 0 3rem;
  position: relative;
}

/* 设备显示控制 */
.carousel-container {
  display: block;
}

/* 桌面端隐藏 */
@media (min-width: 1024px) {
  .hide-on-desktop {
    display: none !important;
  }
}

/* 平板端隐藏 */
@media (min-width: 641px) and (max-width: 1023px) {
  .hide-on-tablet {
    display: none !important;
  }
}

/* 移动端隐藏 */
@media (max-width: 640px) {
  .hide-on-mobile {
    display: none !important;
  }
}

/* 轮播标题 */
.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--uno-colors-primary);
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  display: block;
  width: 50px;
  height: 3px;
  background-color: var(--uno-colors-primary);
  margin: 0.5rem auto 0;
  border-radius: 3px;
}

/* 轮播包装器 */
.carousel-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  overflow: hidden;
}

/* 轮播轨道 */
.carousel-track {
  display: flex;
  width: 100%;
  overflow: hidden;
  position: relative;
  height: 100%;
  min-height: 280px; /* 进一步减小高度 */
}

/* 轮播项 */
.carousel-item {
  flex: 0 0 100%;
  width: 100%;
  position: absolute;
  transition: transform 0.5s ease;
  padding: 0 1rem;
  height: 100%; /* 确保高度占满 */
}

/* 系列卡片 */
.series-card {
  background-color: #f8fafc; /* 淡蓝灰色背景 */
  background-image: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); /* 渐变背景 */
  border-radius: 12px;
  padding: 1.25rem;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden; /* 隐藏溢出内容 */
  max-height: 280px; /* 进一步减小高度 */
  position: relative;
}

/* 添加微妙的装饰元素 */
.series-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, transparent 50%, rgba(var(--uno-colors-primary-rgb), 0.1) 50%);
  border-radius: 0 12px 0 60px;
  z-index: 0;
  opacity: 0.7;
}

.series-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 28px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0);
}

/* 系列标题 */
.series-header {
  margin-bottom: 0.5rem; /* 减少底部间距 */
  position: relative;
  padding-bottom: 0.5rem; /* 减少底部内边距 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  z-index: 1; /* 确保内容在装饰元素之上 */
}

.series-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 限制为1行 */
  -webkit-box-orient: vertical;
}

.series-title a {
  color: var(--uno-colors-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.series-title a:hover {
  text-decoration: none;
  opacity: 0.85;
}

/* 系列元数据 */
.series-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
  font-size: 0.8rem;
}

.series-count {
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
}

/* 系列状态 */
.series-status {
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  letter-spacing: 0.02em;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 不同状态的样式 */
.status-ongoing {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.2);
}

.status-planned {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid rgba(250, 140, 22, 0.2);
}

.status-paused {
  background-color: #fff1f0;
  color: #f5222d;
  border: 1px solid rgba(245, 34, 45, 0.2);
}

/* 文章列表 */
.series-articles {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 0 0 auto; /* 不再使用flex-grow，使列表只占用所需空间 */
  position: relative;
  z-index: 1; /* 确保内容在装饰元素之上 */
}

.article-item {
  margin-bottom: 0.3rem; /* 减少项目间距 */
  line-height: 1.3;
  position: relative;
}

.article-item:last-child {
  margin-bottom: 0; /* 最后一项不需要底部间距 */
}

.article-link {
  display: flex;
  text-decoration: none;
  color: #333;
  padding: 0.35rem 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  align-items: center; /* 居中对齐 */
  font-size: 0.9rem;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.08); /* 添加虚线下划线 */
}

.article-link:hover {
  background-color: rgba(0, 0, 0, 0.04);
  text-decoration: none;
  color: var(--uno-colors-primary);
  transform: translateX(2px);
  border-bottom-color: var(--uno-colors-primary); /* 悬停时改变下划线颜色 */
}

.article-order {
  margin-right: 0.5rem;
  font-weight: 600;
  color: var(--uno-colors-primary);
  flex-shrink: 0; /* 防止序号被压缩 */
  opacity: 0.8;
  font-size: 0.85rem;
}

.article-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 单行显示，超出部分用省略号 */
  line-height: 1.3;
}

/* 系列页脚 */
.series-footer {
  margin-top: auto; /* 自动占用剩余空间 */
  text-align: center;
  padding-top: 0.5rem; /* 增加一点顶部间距 */
  position: relative;
  z-index: 1; /* 确保内容在装饰元素之上 */
  display: flex;
  align-items: flex-end; /* 将按钮对齐到底部 */
  justify-content: center;
  flex: 1 0 auto; /* 允许伸展以填充空间 */
}

@media (max-width: 480px) {
  .series-footer {
    padding-top: 0.2rem; /* 移动端减少顶部间距 */
  }
}

.view-series-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.4rem 1rem;
  background-color: var(--uno-colors-primary);
  color: white;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  width: 80%;
  max-width: 180px;
}

.view-series-button:hover {
  opacity: 0.95;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* 导航按钮 */
.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: white;
  border: none;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  color: var(--uno-colors-primary);
  font-size: 1.25rem;
  opacity: 0.9;
}

.carousel-nav:hover {
  background-color: var(--uno-colors-primary);
  color: white;
  opacity: 1;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.prev-button {
  left: -5px;
}

.next-button {
  right: -5px;
}

/* 指示器 */
.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ddd;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  opacity: 0.6;
}

.indicator-dot.active {
  background-color: var(--uno-colors-primary);
  transform: scale(1.2);
  opacity: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 暗色模式适配 */
.dark .series-card {
  background-color: #1a1a1a;
  background-image: linear-gradient(135deg, #1a1a1a 0%, #222222 100%);
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.dark .series-card::before {
  background: linear-gradient(135deg, transparent 50%, rgba(var(--uno-colors-primary-rgb), 0.15) 50%);
  opacity: 0.5;
}

.dark .series-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark .series-count {
  color: #aaa;
}

.dark .article-link {
  color: #e5e7eb;
  border-bottom-color: rgba(255, 255, 255, 0.1); /* 暗色模式下的下划线 */
}

.dark .article-link:hover {
  background-color: rgba(255, 255, 255, 0.07);
  color: var(--uno-colors-primary);
  border-bottom-color: var(--uno-colors-primary); /* 悬停时改变下划线颜色 */
}

.dark .carousel-nav {
  background-color: #333;
  color: #e5e7eb;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.dark .carousel-nav:hover {
  background-color: var(--uno-colors-primary);
  color: #222;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.dark .indicator-dot {
  background-color: #555;
}

.dark .indicator-dot.active {
  background-color: var(--uno-colors-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .view-series-button {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.dark .view-series-button:hover {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
}

/* 暗色模式状态样式 */
.dark .status-ongoing {
  background-color: #003a8c;
  color: #91caff;
  border-color: rgba(24, 144, 255, 0.3);
}

.dark .status-completed {
  background-color: #135200;
  color: #95de64;
  border-color: rgba(82, 196, 26, 0.3);
}

.dark .status-planned {
  background-color: #613400;
  color: #ffc069;
  border-color: rgba(250, 140, 22, 0.3);
}

.dark .status-paused {
  background-color: #5c0011;
  color: #ff7875;
  border-color: rgba(245, 34, 45, 0.3);
}

/* 响应式样式 */
/* 平板模式特定样式 */
@media (min-width: 769px) and (max-width: 1023px) {
  /* 平板模式下的轮播箭头样式 */
  .carousel-nav {
    width: 46px;
    height: 46px;
    font-size: 1.5rem;
    opacity: 1;
    z-index: 20;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background-color: rgba(255, 255, 255, 0.9);
  }

  .prev-button {
    left: -13px;
  }

  .next-button {
    right: -13px;
  }

  /* 平板模式下的暗色主题轮播箭头样式 */
  .dark .carousel-nav {
    background-color: rgba(51, 51, 51, 0.9);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

/* 移动设备样式 */
@media (max-width: 768px) {
  .carousel-track {
    min-height: 300px;
  }

  .series-card {
    padding: 1rem;
    max-height: 300px;
  }

  .series-title {
    font-size: 1rem;
  }

  .series-header {
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .article-link {
    padding: 0.25rem 0.4rem;
    font-size: 0.85rem;
  }

  .article-item {
    margin-bottom: 0.35rem;
  }

  .view-series-button {
    padding: 0.35rem 0.8rem;
    font-size: 0.85rem;
  }
}

/* 中等尺寸手机 */
@media (max-width: 576px) {
  .carousel-track {
    min-height: 230px; /* 减小高度 */
  }

  .series-card {
    padding: 0.9rem;
    max-height: 230px; /* 减小高度 */
  }

  .series-title {
    font-size: 0.95rem;
  }

  .article-link {
    padding: 0.25rem 0.4rem;
    font-size: 0.8rem;
  }

  .carousel-nav {
    width: 30px;
    height: 30px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .carousel-track {
    min-height: 210px; /* 进一步减小移动端高度 */
  }

  .series-card {
    padding: 0.8rem;
    max-height: 210px; /* 进一步减小移动端卡片高度 */
  }

  .series-header {
    margin-bottom: 0.3rem;
    padding-bottom: 0.3rem;
  }

  .series-title {
    font-size: 0.95rem;
    margin-bottom: 0.2rem;
  }

  .series-meta {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.4rem;
    font-size: 0.75rem;
  }

  .series-status {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }

  .article-item {
    margin-bottom: 0.15rem;
  }

  .article-link {
    padding: 0.2rem 0.3rem;
    font-size: 0.8rem;
  }

  .article-order {
    font-size: 0.75rem;
  }

  .view-series-button {
    padding: 0.3rem 0.7rem;
    font-size: 0.8rem;
    max-width: 150px;
  }

  .carousel-nav {
    width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .prev-button {
    left: -5px;
  }

  .next-button {
    right: -5px;
  }

  .indicator-dot {
    width: 6px;
    height: 6px;
  }
}

/* 超小屏幕手机 */
@media (max-width: 360px) {
  .carousel-track {
    min-height: 190px; /* 进一步减小高度 */
  }

  .series-card {
    padding: 0.7rem;
    max-height: 190px; /* 进一步减小卡片高度 */
  }

  .series-title {
    font-size: 0.9rem;
  }

  .article-link {
    padding: 0.2rem 0.3rem;
    font-size: 0.75rem;
  }

  .article-order {
    margin-right: 0.3rem;
  }

  .view-series-button {
    padding: 0.25rem 0.6rem;
    font-size: 0.75rem;
  }

  .carousel-nav {
    width: 26px;
    height: 26px;
    font-size: 0.85rem;
  }

  .prev-button {
    left: -3px;
  }

  .next-button {
    right: -3px;
  }
}
