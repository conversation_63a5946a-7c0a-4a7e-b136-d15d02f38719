// 在页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  // 获取搜索输入框
  const searchInput = document.getElementById('search-input');

  // 如果找到搜索输入框，添加输入事件监听器
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      // 延迟执行，等待原始搜索结果渲染完成
      setTimeout(enhanceSearchResults, 500);
    });

    // 从URL获取查询参数
    const urlParams = new URLSearchParams(window.location.search);
    const queryParam = urlParams.get('q');

    // 如果URL中有查询参数，延迟执行增强功能
    if (queryParam) {
      setTimeout(enhanceSearchResults, 1000);
    }
  }
});

// 增强搜索结果显示
function enhanceSearchResults() {
  console.log('Enhancing search results...');

  // 获取搜索输入框的值
  const searchInput = document.getElementById('search-input');
  if (!searchInput) return;

  const query = searchInput.value.trim();
  if (!query) return;

  console.log('Search query:', query);

  // 获取所有搜索结果项
  const resultItems = document.querySelectorAll('.results-container > div');
  console.log('Found result items:', resultItems.length);

  // 遍历每个结果项
  resultItems.forEach(item => {
    // 添加样式
    item.style.padding = '1rem';
    item.style.marginBottom = '1.5rem';
    item.style.border = '1px solid #eaeaea';
    item.style.borderRadius = '8px';
    item.style.transition = 'all 0.3s ease';

    // 获取标题和描述
    const titleElement = item.querySelector('h2 a, h3 a');
    const descElement = item.querySelector('p, .post-description, .result-context');

    if (titleElement) {
      // 高亮标题中的关键词
      const title = titleElement.textContent;
      titleElement.innerHTML = highlightText(title, query);
    }

    if (descElement) {
      // 高亮描述中的关键词
      const desc = descElement.textContent;
      descElement.innerHTML = highlightText(desc, query);
    }
  });

  // 添加鼠标悬停效果
  const style = document.createElement('style');
  style.textContent = `
    .results-container > div:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transform: translateY(-2px);
    }

    .dark .results-container > div {
      border-color: #333;
    }

    .dark .results-container > div:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  `;
  document.head.appendChild(style);
}

// 高亮匹配的文本
function highlightText(text, query) {
  if (!text || !query || query.trim() === '') return text || '';

  try {
    // 先将文本转换为字符串
    const textStr = String(text);

    // 转义正则表达式特殊字符
    const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // 创建正则表达式
    const regex = new RegExp(`(${escapedQuery})`, 'gi');

    // 使用内联样式替换所有匹配项
    return textStr.replace(regex, '<mark style="background-color: #ffdc00; color: #000; padding: 0 2px; border-radius: 2px; font-weight: bold;">$1</mark>');
  } catch (error) {
    console.error('Highlight error:', error);
    return text || '';
  }
}
