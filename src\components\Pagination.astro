---
const { translate: t } = Astro.locals

interface Props {
  showLeft?: boolean
  leftTitle?: string
  leftUrl?: string

  showRight?: boolean
  rightTitle?: string
  rightUrl?: string

  showPageCount?: boolean
  currentPage?: number
  totalPage?: number
}

const {
  showLeft = true,
  showRight = true,
  leftTitle,
  rightTitle,
  leftUrl,
  rightUrl,
  showPageCount = true,
  currentPage,
  totalPage,
} = Astro.props
---

<footer class="pagination-footer">
  <div class="pagination">
    {
      showLeft && (
        <a title={leftTitle} href={leftUrl} class="pagination-item pagination-prev">
          <span class="pagination-icon i-mdi-chevron-double-left" />
          <span class="pagination-text">{leftTitle}</span>
        </a>
      )
    }

    {!showLeft && !showPageCount && <div class="pagination-spacer-left" />}

    {
      showPageCount && (
        <div class="pagination-info">
          {t('page_number', currentPage)} / {t('page_count', totalPage)}
        </div>
      )
    }

    {!showRight && !showPageCount && <div class="pagination-spacer-right" />}

    {
      showRight && (
        <a title={rightTitle} href={rightUrl} class="pagination-item pagination-next">
          <span class="pagination-text">{rightTitle}</span>
          <span class="pagination-icon i-mdi-chevron-double-right" />
        </a>
      )
    }
  </div>
</footer>

<style>
  .pagination-footer {
    margin-top: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pagination-info {
    font-size: 0.95rem;
    color: var(--uno-colors-primary, #2e405b);
    opacity: 0.8;
    text-align: center;
    padding: 0.75rem 1rem;
    min-width: 120px;
  }

  /* 使用全局样式中的pagination类，这里只添加特定样式 */
  .pagination-item {
    display: flex;
    align-items: center;
    text-decoration: none;
    min-width: 120px;
    justify-content: center;
  }

  .pagination-prev {
    justify-content: flex-start;
  }

  .pagination-next {
    justify-content: flex-end;
  }

  .pagination-text {
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    line-height: 1.5;
    font-size: 0.95rem;
    max-width: 80%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 0.25rem;
  }

  .pagination-icon {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    display: inline-block;
    vertical-align: middle;
    margin: 0 0.5rem;
  }

  /* 暗色模式适配已在全局样式中定义 */

  /* 占位元素样式 */
  .pagination-spacer-left,
  .pagination-spacer-right {
    flex: 1;
    min-width: 120px;
  }
</style>
