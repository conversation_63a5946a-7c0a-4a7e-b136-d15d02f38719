---
/**
 * 图片灯箱组件
 * 提供全屏查看图片的功能，包括：
 * 1. 放大缩小
 * 2. 前后导航
 * 3. 触摸手势支持
 * 4. 键盘快捷键
 */
import { themeConfig } from '../.config/index';
import type { ConfigLightbox } from '../types';

// 获取灯箱配置
const lightboxConfig = (themeConfig.lightbox || {}) as ConfigLightbox;
const enabled = lightboxConfig.enable !== false;
---

{enabled && (
  <div id="image-lightbox" class="lightbox">
    <div class="lightbox-overlay"></div>

    <button class="lightbox-close" aria-label="关闭">
      <span class="i-mdi-close"></span>
    </button>

    <button class="lightbox-prev" aria-label="上一张">
      <span class="i-mdi-chevron-left"></span>
    </button>

    <button class="lightbox-next" aria-label="下一张">
      <span class="i-mdi-chevron-right"></span>
    </button>

    <div class="lightbox-container">
      <div class="lightbox-content">
        <img src="" alt="" class="lightbox-image" />
      </div>
    </div>

    <div class="lightbox-caption"></div>

    <div class="lightbox-toolbar">
      <button class="lightbox-zoom-in" aria-label="放大">
        <span class="i-mdi-plus"></span>
      </button>
      <button class="lightbox-zoom-out" aria-label="缩小">
        <span class="i-mdi-minus"></span>
      </button>
      <button class="lightbox-zoom-reset" aria-label="重置缩放">
        <span class="i-mdi-refresh"></span>
      </button>
    </div>

    <div class="lightbox-counter"></div>
  </div>
)}

<script>
  // 使用立即执行函数确保作用域隔离
  (function() {
    // 初始化灯箱功能
    function initLightbox() {
      // 获取DOM元素
      const lightbox = document.getElementById('image-lightbox');
      if (!lightbox) return;

      const overlay = lightbox.querySelector('.lightbox-overlay');
      const closeBtn = lightbox.querySelector('.lightbox-close');
      const prevBtn = lightbox.querySelector('.lightbox-prev');
      const nextBtn = lightbox.querySelector('.lightbox-next');
      const content = lightbox.querySelector('.lightbox-content');
      const image = lightbox.querySelector('.lightbox-image');
      const caption = lightbox.querySelector('.lightbox-caption');
      const counter = lightbox.querySelector('.lightbox-counter');
      const zoomInBtn = lightbox.querySelector('.lightbox-zoom-in');
      const zoomOutBtn = lightbox.querySelector('.lightbox-zoom-out');
      const zoomResetBtn = lightbox.querySelector('.lightbox-zoom-reset');

      // 状态变量
      let currentIndex = 0;
      let images: HTMLImageElement[] = [];
      let scale = 1;
      let translateX = 0;
      let translateY = 0;
      let startX = 0;
      let startY = 0;
      let lastX = 0;
      let lastY = 0;
      let isDragging = false;

      // 初始化图片集合
      function updateImagesList() {
        // 获取所有带有lightbox类的图片
        images = Array.from(document.querySelectorAll('img.lightbox-trigger'));
      }

      // 打开灯箱
      function openLightbox(index: number) {
        updateImagesList();
        if (images.length === 0) return;

        currentIndex = index;
        const currentImage = images[currentIndex];

        // 设置图片源
        if (image) {
          // 尝试获取原始大图
          const originalSrc = currentImage.dataset.original || currentImage.src;
          (image as HTMLImageElement).src = originalSrc;
          (image as HTMLImageElement).alt = currentImage.alt || '';

          // 重置缩放和位置
          resetZoom();
        }

        // 设置标题
        if (caption) {
          caption.textContent = currentImage.alt || '';
        }

        // 更新计数器
        updateCounter();

        // 显示灯箱
        if (lightbox) {
          lightbox.classList.add('active');

          // 禁止背景滚动
          document.body.style.overflow = 'hidden';
        }
      }

      // 关闭灯箱
      function closeLightbox() {
        if (lightbox) {
          lightbox.classList.remove('active');

          // 恢复背景滚动
          document.body.style.overflow = '';

          // 延迟清除图片源，避免闪烁
          setTimeout(() => {
            if (image) (image as HTMLImageElement).src = '';
          }, 300);
        }
      }

      // 显示上一张图片
      function showPrevImage() {
        if (images.length <= 1) return;

        currentIndex = (currentIndex - 1 + images.length) % images.length;
        const currentImage = images[currentIndex];

        // 设置图片源
        if (image) {
          const originalSrc = currentImage.dataset.original || currentImage.src;
          (image as HTMLImageElement).src = originalSrc;
          (image as HTMLImageElement).alt = currentImage.alt || '';

          // 重置缩放和位置
          resetZoom();
        }

        // 设置标题
        if (caption) {
          caption.textContent = currentImage.alt || '';
        }

        // 更新计数器
        updateCounter();
      }

      // 显示下一张图片
      function showNextImage() {
        if (images.length <= 1) return;

        currentIndex = (currentIndex + 1) % images.length;
        const currentImage = images[currentIndex];

        // 设置图片源
        if (image) {
          const originalSrc = currentImage.dataset.original || currentImage.src;
          (image as HTMLImageElement).src = originalSrc;
          (image as HTMLImageElement).alt = currentImage.alt || '';

          // 重置缩放和位置
          resetZoom();
        }

        // 设置标题
        if (caption) {
          caption.textContent = currentImage.alt || '';
        }

        // 更新计数器
        updateCounter();
      }

      // 更新计数器
      function updateCounter() {
        if (counter && images.length > 1) {
          counter.textContent = `${currentIndex + 1} / ${images.length}`;
          (counter as HTMLElement).style.display = 'block';
        } else if (counter) {
          (counter as HTMLElement).style.display = 'none';
        }

        // 如果只有一张图片，隐藏导航按钮
        if (prevBtn && nextBtn) {
          if (images.length <= 1) {
            (prevBtn as HTMLElement).style.display = 'none';
            (nextBtn as HTMLElement).style.display = 'none';
          } else {
            (prevBtn as HTMLElement).style.display = 'block';
            (nextBtn as HTMLElement).style.display = 'block';
          }
        }
      }

      // 缩放功能
      function zoomIn() {
        scale += 0.25;
        if (scale > 3) scale = 3; // 最大缩放3倍
        updateTransform();
      }

      function zoomOut() {
        scale -= 0.25;
        if (scale < 0.5) scale = 0.5; // 最小缩放0.5倍
        updateTransform();
      }

      function resetZoom() {
        scale = 1;
        translateX = 0;
        translateY = 0;
        updateTransform();
      }

      function updateTransform() {
        if (content) {
          (content as HTMLElement).style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`;
        }
      }

      // 拖动功能
      function startDrag(e: MouseEvent | TouchEvent) {
        if (scale <= 1) return; // 只有放大时才能拖动

        isDragging = true;
        startX = 'clientX' in e ? e.clientX : (e.touches?.[0]?.clientX || 0);
        startY = 'clientY' in e ? e.clientY : (e.touches?.[0]?.clientY || 0);
        lastX = translateX;
        lastY = translateY;

        if (content) {
          (content as HTMLElement).style.transition = 'none'; // 拖动时禁用过渡效果
        }
      }

      function drag(e: MouseEvent | TouchEvent) {
        if (!isDragging) return;

        const clientX = 'clientX' in e ? e.clientX : (e.touches?.[0]?.clientX || 0);
        const clientY = 'clientY' in e ? e.clientY : (e.touches?.[0]?.clientY || 0);

        translateX = lastX + (clientX - startX);
        translateY = lastY + (clientY - startY);

        updateTransform();
      }

      function endDrag() {
        isDragging = false;

        if (content) {
          (content as HTMLElement).style.transition = 'transform 0.3s ease-out'; // 恢复过渡效果
        }
      }

      // 事件监听
      if (closeBtn) {
        closeBtn.addEventListener('click', closeLightbox);
      }

      if (overlay) {
        overlay.addEventListener('click', closeLightbox);
      }

      if (prevBtn) {
        prevBtn.addEventListener('click', showPrevImage);
      }

      if (nextBtn) {
        nextBtn.addEventListener('click', showNextImage);
      }

      if (zoomInBtn) {
        zoomInBtn.addEventListener('click', zoomIn);
      }

      if (zoomOutBtn) {
        zoomOutBtn.addEventListener('click', zoomOut);
      }

      if (zoomResetBtn) {
        zoomResetBtn.addEventListener('click', resetZoom);
      }

      // 触摸滑动支持
      let touchStartX = 0;
      let touchEndX = 0;

      if (lightbox) {
        lightbox.addEventListener('touchstart', (e) => {
          touchStartX = e.changedTouches[0].screenX;
        }, { passive: true });

        lightbox.addEventListener('touchend', (e) => {
          touchEndX = e.changedTouches[0].screenX;
          handleSwipe();
        }, { passive: true });
      }

      function handleSwipe() {
        const swipeThreshold = 50; // 最小滑动距离
        if (touchEndX < touchStartX - swipeThreshold) {
          // 向左滑动，显示下一个
          showNextImage();
        } else if (touchEndX > touchStartX + swipeThreshold) {
          // 向右滑动，显示上一个
          showPrevImage();
        }
      }

      if (content) {
        (content as HTMLElement).addEventListener('mousedown', startDrag as EventListener);
        (content as HTMLElement).addEventListener('touchstart', startDrag as EventListener, { passive: true });

        window.addEventListener('mousemove', drag as EventListener);
        window.addEventListener('touchmove', drag as EventListener, { passive: true });

        window.addEventListener('mouseup', endDrag);
        window.addEventListener('touchend', endDrag);
      }

      // 键盘快捷键
      window.addEventListener('keydown', (e) => {
        if (!lightbox.classList.contains('active')) return;

        switch (e.key) {
          case 'Escape':
            closeLightbox();
            break;
          case 'ArrowLeft':
            showPrevImage();
            break;
          case 'ArrowRight':
            showNextImage();
            break;
          case '+':
            zoomIn();
            break;
          case '-':
            zoomOut();
            break;
          case '0':
            resetZoom();
            break;
        }
      });

      // 为所有图片添加点击事件
      function setupImageClickHandlers() {
        updateImagesList();

        // 使用事件委托，提高性能
        document.addEventListener('click', (e) => {
          const target = e.target as HTMLElement;

          // 检查点击的是否是带有lightbox-trigger类的图片
          if (target && target.tagName === 'IMG' && target.classList.contains('lightbox-trigger')) {
            e.preventDefault();

            // 找到图片在集合中的索引
            const index = images.indexOf(target as HTMLImageElement);
            if (index !== -1) {
              openLightbox(index);
            }
          }
        });
      }

      // 在DOM内容加载后初始化
      document.addEventListener('DOMContentLoaded', setupImageClickHandlers);

      // 导出公共API，允许其他脚本调用
      (window as any).lightbox = {
        open: openLightbox,
        close: closeLightbox,
        next: showNextImage,
        prev: showPrevImage,
        updateImages: updateImagesList
      };
    }

    // 初始化灯箱
    if (document.getElementById('image-lightbox')) {
      initLightbox();
    }
  })();
</script>
