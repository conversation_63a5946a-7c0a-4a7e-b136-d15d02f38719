import type { Link, Meta } from 'astro-seo'
import type {
  AvailableLanguage,
  BooleanString,
  InputPosition,
  Loading,
  Mapping,
  Repo,
  Theme,
} from 'giscus'
import type { LANGUAGES } from '../i18n.ts'

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
}

export interface ThemeConfig {
  site: ConfigSite
  appearance: ConfigAppearance
  seo: ConfigSEO
  comment: Partial<ConfigComment>
  rss: ConfigRSS
  analytics: ConfigAnalytics
  latex: ConfigLaTeX
  donate: ConfigDonate
  relatedPosts: ConfigRelatedPosts
  carousel: ConfigCarousel
  imageOptimization: ConfigImageOptimization
  adsense?: ConfigAdSense
  lightbox?: ConfigLightbox
}

export type UserConfig = DeepPartial<ThemeConfig>

export interface ConfigSite {
  title: string
  subtitle: string
  author: string
  description: string
  website: string
  pageSize: number
  socialLinks: { name: string, href: string, title?: string }[]
  navLinks: { name: string, href: string, icon?: string }[]
  categoryMap: { name: string, path: string }[]
  tagMap: { name: string, path: string }[]
  seriesMap: { name: string, path: string }[]
  footer: string[]
}

export interface ConfigAppearance {
  theme: 'light' | 'dark' | 'system'
  locale: keyof typeof LANGUAGES
  colorsDark: Colors
  colorsLight: Colors
  fonts: Fonts
}

export interface ConfigSEO {
  twitter: string
  meta: Partial<Meta>[]
  link: Partial<Link>[]
}

export interface ConfigComment {
  disqus: Disqus
  giscus: Giscus
  twikoo: Twikoo
}

export interface ConfigRSS {
  fullText?: boolean
  /** https://github.com/RSSNext/follow */
  follow?: { feedId: string, userId: string }
}

export interface ConfigAnalytics {
  /** google analytics */
  googleAnalyticsId: string
  umamiAnalyticsId: string
}

export interface ConfigLaTeX {
  katex: boolean
}

interface Colors {
  primary: string
  background: string
}

interface Fonts {
  header: string
  ui: string
  // TODO: 未实现
  _article?: string
  _code?: string
}

interface Twikoo {
  envId: string
  region?: string
  lang?: string
  path?: string
}

interface Disqus {
  shortname: string
}

interface Giscus {
  repo: Repo
  repoId?: string
  category?: string
  categoryId?: string
  mapping?: Mapping
  term?: string
  strict: BooleanString
  reactionsEnabled: BooleanString
  emitMetadata: BooleanString
  inputPosition: InputPosition
  theme: Theme
  lang: AvailableLanguage
  loading: Loading
}

export interface ConfigDonate {
  enable: boolean
  paypal?: string
  crypto?: {
    btc?: string
    eth?: string
    usdt?: string
    [key: string]: string | undefined
  }
}



export interface ConfigRelatedPosts {
  enable: boolean
  maxPosts: number
  randomize: boolean
}

export interface ConfigCarousel {
  enable: boolean
  maxItems: number
  showArticles: number
  sortBy: 'latest' | 'articleCount' | 'custom'
  autoplay: boolean
  autoplaySpeed: number
  // 自定义系列列表
  customSeries?: string[]
  // 自定义排序函数 (可选)
  customSort?: 'asc' | 'desc' | 'alphabetical' | 'random'
  // 设备显示控制
  deviceDisplay?: {
    desktop?: boolean  // 桌面端是否显示
    tablet?: boolean   // 平板端是否显示
    mobile?: boolean   // 移动端是否显示
  }
}

export interface ConfigCDN {
  enable: boolean
  provider: string
  domain: string
  options: Record<string, string | number | boolean | undefined>
}

export interface ConfigImageOptimization {
  enable: boolean
  quality: number
  formats: ('avif' | 'webp' | 'jpeg' | 'png' | 'svg')[]
  widths: number[]
  usePlaceholder: boolean
  placeholderSize: number
  cdn: ConfigCDN
}

export interface ConfigLightbox {
  enable: boolean
  animationSpeed?: number
  bgOpacity?: number
  zoomStep?: number
  maxZoom?: number
  minZoom?: number
}

export interface ConfigAdSense {
  enable: boolean
  clientId: string
}
