---
import type { Post } from '~/types'
import { formatDate } from '~/utils'
import PostCategory from './PostCategory.astro'
import PostTag from './PostTag.astro'

interface Props {
  post: Post
}

const { post } = Astro.props
const { translate: t } = Astro.locals

const categoryList = post.data.categories ?? []
const tagList = post.data.tags ?? []
---

<header flex="~ col gap-2">
  <h1 class="post-title">
    <a class="not-prose" href={`/posts/${post.id}/`}>
      {post.data.pin && <span class="pin-icon" title="置顶文章">📌</span>}
      {post.data.title}
    </a>
  </h1>
  <div class="text-3.5">
    <span>{t(post.data.modDate ? 'updated_at' : 'posted_at')}</span>
    <time>{formatDate(post.data.modDate ?? post.data.pubDate)}</time>
    <div class="meta-items">
      <div class="categories">
        {categoryList.map((category) => <PostCategory category={category} />)}
      </div>
      {tagList.length > 0 && (
        <div class="tags">
          {tagList.map((tag) => <PostTag tag={tag} />)}
        </div>
      )}
    </div>
  </div>
</header>

<style>
  .pin-icon {
    display: inline-block;
    margin-right: 0.25rem;
    font-size: 0.9em;
    vertical-align: middle;
  }

  .meta-items {
    margin-top: 0.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .categories, .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  /* 文章标题样式 - 确保在移动端正确显示 */
  @media (max-width: 768px) {
    .post-title {
      font-size: 1.5rem !important; /* 与mobile-fix.css保持一致 */
      line-height: 1.4 !important; /* 增加行高，提高多行标题的可读性 */
      margin-bottom: 0.5rem !important;
      word-break: break-word !important;
      font-weight: 600 !important; /* 减轻字重，提高可读性 */
    }

    .post-title a {
      display: inline-block !important;
      word-break: break-word !important;
    }

    /* 文章列表标题链接样式 - 显式添加下划线 */
    .post-list .post-title a {
      text-decoration: underline !important;
      text-decoration-color: var(--uno-colors-primary) !important;
      text-decoration-thickness: 0.1em !important;
      text-underline-offset: 2px !important;
    }


  }

  /* 小屏幕设备的额外调整 */
  @media (max-width: 480px) {
    .post-title {
      font-size: 1.35rem !important;
    }
  }
</style>
