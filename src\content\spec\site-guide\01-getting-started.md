---
title: "1. 基础入门"
pubDate: 2023-12-31
---

# 1. 基础入门

在开始使用川流主题之前，让我们先了解一些基础知识，包括项目结构、开发环境设置和基本命令。

## 项目结构概览

川流主题基于 Astro 构建，项目结构清晰明了：

```
川流主题/
├── public/          # 静态资源目录（图片、字体等）
├── src/             # 源代码目录
│   ├── .config/     # 主题配置文件
│   ├── components/  # 组件目录
│   ├── content/     # 内容目录（文章、页面等）
│   ├── layouts/     # 布局模板
│   ├── pages/       # 页面路由
│   ├── styles/      # 样式文件
│   └── utils/       # 工具函数
├── astro.config.ts  # Astro 配置文件
├── package.json     # 项目依赖
└── tsconfig.json    # TypeScript 配置
```

### 重要目录说明

- **src/content/posts/**：存放所有博客文章的 Markdown 文件
- **src/.config/user.ts**：主题的用户配置文件，大部分自定义设置都在这里
- **src/pages/**：网站的页面路由，每个文件对应一个页面
- **public/**：存放静态资源，如图片、字体等

## 开发环境设置

### 前提条件

在开始之前，请确保您的电脑上已安装：

1. **Node.js**（推荐 v18.0.0 或更高版本）
2. **npm**、**yarn** 或 **pnpm** 包管理器

### 安装步骤

1. **克隆项目**（如果您是从头开始）：

```bash
# 使用 Git 克隆项目
git clone https://github.com/yourusername/your-blog-repo.git my-blog

# 进入项目目录
cd my-blog
```

2. **安装依赖**：

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn

# 或使用 pnpm
pnpm install
```

## 基本命令使用

### 开发服务器

启动本地开发服务器，实时预览您的更改：

```bash
npm run dev
```

启动后，您可以在浏览器中访问 `http://localhost:4321` 查看您的网站。

### 构建网站

生成用于部署的静态文件：

```bash
npm run build
```

构建完成后，生成的文件将位于 `dist/` 目录中。

### 预览构建结果

在部署前预览构建结果：

```bash
npm run preview
```

### 创建新文章

快速创建新文章（如果配置了此功能）：

```bash
npm run new-post
```

## 部署网站

川流主题生成的是静态网站，可以部署到任何静态网站托管服务。以下是几个推荐的部署平台：

### Vercel（推荐）

1. 在 [Vercel](https://vercel.com) 上创建账号
2. 导入您的 Git 仓库
3. 选择 Astro 框架预设
4. 点击部署

### Netlify

1. 在 [Netlify](https://netlify.com) 上创建账号
2. 导入您的 Git 仓库
3. 构建命令设置为 `npm run build`
4. 发布目录设置为 `dist`
5. 点击部署

### GitHub Pages

1. 在 `astro.config.ts` 中设置正确的 `site` 和 `base` 值
2. 运行 `npm run build`
3. 将 `dist` 目录推送到 GitHub 仓库的 `gh-pages` 分支

## 下一步

现在您已经了解了基础知识，接下来可以：

1. [创建您的第一篇文章](./02-content-management.md)
2. [自定义网站外观](./03-theme-customization.md)
3. [配置功能模块](./04-features-configuration.md)

如果您在设置过程中遇到任何问题，请参考[常见问题与故障排除](./07-troubleshooting.md)章节。
