---
title: "文章模板使用指南"
pubDate: 2023-11-15
---

# 文章模板使用指南

本指南介绍如何使用博客文章模板创建新的文章，以及各种Front-matter字段的用法和最佳实践。

## 模板位置

文章模板位于 `src/content/templates/post-template.md`，您可以复制此文件作为创建新文章的起点。

## Front-matter字段说明

### 必填字段

- **title**: 文章标题，必须填写
- **pubDate**: 发布日期，格式为`YYYY-MM-DD`或`YYYY-MM-DD HH:MM:SS`
- **categories**: 文章分类，必须是数组形式，至少包含一个分类

### 推荐填写字段

- **description**: 文章描述，用于SEO和元数据
- **summary**: 文章摘要，用于文章列表显示，如不填写则自动截取正文
- **tags**: 文章标签，数组形式
- **modDate**: 如果文章有更新，填写最后修改日期

### 特殊功能字段

- **draft**: 设置为`true`表示草稿，在生产环境中不会显示
- **pin**: 设置为`true`可将文章置顶
- **pinOrder**: 置顶文章的排序顺序，数字越小越靠前
- **banner**: 文章banner图片，路径相对于文章所在目录
- **slug**: 自定义URL路径，允许您为文章设置自定义URL，而不使用默认的文件名

### 系列文章字段

- **series**: 系列文章设置，包含以下子字段：
  - **name**: 系列名称，相同系列的文章必须使用相同的名称
  - **order**: 在系列中的顺序，数字越小越靠前

## 使用方法

### 手动创建文章

1. 在`src/content/posts`目录下创建一个新的`.md`或`.mdx`文件
2. 复制`src/content/templates/post-template.md`的内容到新文件
3. 根据需要填写或删除Front-matter字段
4. 编写文章正文内容

### 使用脚本创建文章

您也可以使用项目中的创建文章脚本：

```bash
pnpm new-post
```

这个脚本会自动生成带有基本Front-matter的新文章文件。

## 示例文章

在`src/content/posts/astro-blog-tutorial.md`中有一篇示例文章，展示了如何在实际写作中使用这些Front-matter字段。

## 最佳实践

1. **保持一致性**：为所有文章使用相同的Front-matter结构
2. **使用有意义的分类和标签**：帮助读者找到相关内容
3. **优化SEO**：始终提供描述和适当的标题
4. **定期更新**：对更新的文章添加`modDate`字段
5. **适当使用置顶**：不要过度使用置顶功能，以免降低其效果
6. **图片优化**：确保banner图片经过优化，以提高加载速度
7. **系列文章规划**：在开始写系列文章前，先规划好整个系列的结构和内容
8. **系列命名一致**：确保同一系列的所有文章使用完全相同的系列名称，包括大小写和空格

## 系列文章写作

系列文章是一组相关联的文章，按照特定顺序排列。写作系列文章时，请注意以下几点：

1. **设置系列属性**：在Front-matter中添加`series`字段：

```yaml
series:
  name: "系列名称"
  order: 1  # 在系列中的顺序号
```

2. **保持系列名称一致**：同一系列的所有文章必须使用完全相同的系列名称，包括大小写和空格。

3. **顺序编号**：`order`字段决定文章在系列中的显示顺序，从1开始。

4. **内部链接**：在系列文章中，建议添加指向系列中其他文章的链接，例如：

```markdown
在[上一篇文章](/posts/previous-article)中，我们讨论了...
在下一篇文章中，我们将探讨[XXX主题](/posts/next-article)。
```

5. **系列导航**：系统会自动在系列文章页面添加系列导航组件，显示系列中的所有文章及其顺序。

6. **系列页面**：所有系列文章都会在`/series`页面中列出，用户可以浏览所有系列。

## 自定义URL路径

本博客系统支持为文章设置自定义URL路径，而不使用默认的文件名。这在以下情况下特别有用：

1. 当文件名不够描述性或不够简洁时
2. 当您想为重要文章创建更有意义的URL时
3. 当您想使用中文文件名但希望URL使用英文时

### 使用方法

在文章的Front-matter中添加`slug`字段：

```yaml
---
title: "我的文章标题"
pubDate: 2023-01-01
categories: ["分类1"]
slug: "my-custom-url"  # 自定义URL路径
---
```

使用自定义slug后，文章的URL将变为：`/posts/my-custom-url/`

如果不设置自定义slug，系统将使用文件名作为URL路径。

### 最佳实践

1. **保持简洁**：自定义slug应该简短、描述性强且URL友好
2. **使用英文**：尽量使用英文或拼音，避免使用特殊字符
3. **避免重复**：确保不同文章使用不同的自定义slug
4. **一致性**：为相关文章使用类似的slug命名模式

### URL格式规范

所有URL都应以斜杠结尾，系统会自动将不带斜杠的URL重定向到带斜杠的URL。例如：
- `/posts/my-article` 会被重定向到 `/posts/my-article/`

## 中文分类和标签URL映射

本博客系统支持将中文分类和标签自动映射为英文URL。映射关系在`src/.config/user.ts`文件中配置：

```typescript
// 分类映射表
categoryMap: [
  { name: '技术', path: 'technology' },
  { name: '生活', path: 'life' },
  // 可以添加更多映射...
],

// 标签映射表
tagMap: [
  { name: '编程', path: 'programming' },
  { name: '旅行', path: 'travel' },
  // 可以添加更多映射...
],
```

如果您使用了新的中文分类或标签，建议在这些映射表中添加相应的英文路径。
