---
import { themeConfig } from '~/.config'
import SearchButton from '~/components/SearchButton.astro'

const { navLinks, socialLinks } = themeConfig.site
// 过滤掉搜索相关的导航链接，只在移动端菜单中移除，不影响桌面端
// 同时确保每个导航项都有有效的图标
const filteredNavLinks = navLinks.filter(
  (nav) => !nav.href.includes('/search') && nav.name.toLowerCase() !== 'search' && nav.name.toLowerCase() !== '搜索',
)
const { translate: t } = Astro.locals
---

<div class="mobile-nav-container">
  <!-- 顶部导航栏 -->
  <div class="mobile-nav-header">
    <!-- 左侧：主页图标和网站标题 -->
    <div class="mobile-nav-brand">
      <a href="/" class="home-icon">
        <img src="/images/logo-light.svg" alt="Logo" class="logo-image light-logo" />
        <img src="/images/logo-dark.svg" alt="Logo" class="logo-image dark-logo" />
      </a>
      <a href="/" class="site-title">
        {themeConfig.site.title}
      </a>
    </div>

    <!-- 右侧：搜索按钮和汉堡按钮 -->
    <div class="mobile-nav-actions">
      <SearchButton />
      <button id="mobile-menu-toggle" class="hamburger-button" aria-label="菜单">
        <span class="i-mdi-menu"></span>
      </button>
    </div>
  </div>

  <!-- 弹出式导航菜单 -->
  <div id="mobile-menu" class="mobile-menu">
    <div class="mobile-menu-content">
      <nav class="mobile-menu-nav">
        <ul class="mobile-menu-links">
          {
            filteredNavLinks.map((nav) => (
              <li class="mobile-menu-item">
                <a href={nav.href} class="mobile-menu-link">
                  {nav.icon ? <span class={`i-mdi-${nav.icon} menu-icon`} /> : null}
                  <span class="menu-text">{t(nav.name)}</span>
                </a>
              </li>
            ))
          }
        </ul>

        <!-- 社交媒体图标 -->
        <div class="mobile-social-links">
          {
            socialLinks.map(({ href, name, title }) => {
              // 生成图标类名
              let iconClass = name ? `i-mdi-${name}` : 'i-mdi-link-variant'

              return (
                <a href={href} target="_blank" title={title || name} class="mobile-social-link">
                  <span class:list={[iconClass, 'social-icon']} />
                </a>
              )
            })
          }
        </div>
      </nav>
    </div>
  </div>

  <!-- 菜单展开时的背景遮罩 -->
  <div id="menu-overlay" class="menu-overlay"></div>
</div>

<style>
  /* 移动导航容器 */
  .mobile-nav-container {
    display: none; /* 默认隐藏，只在移动设备显示 */
    position: relative;
    width: 100%;
  }

  /* 顶部导航栏 */
  .mobile-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem; /* 减小上下padding，使导航栏更紧凑 */
    background-color: var(--uno-colors-background, #ffffff);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100; /* 保持适当的层级顺序 */
    height: 3.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* 左侧品牌区域 */
  .mobile-nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .home-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--tw-color-primary, #2e405b);
  }

  .logo-image {
    width: 24px;
    height: 24px; /* 减小logo尺寸，使其与导航栏高度更协调 */
    object-fit: contain;
    transition: transform 0.2s ease;
  }

  /* 亮色模式下显示亮色logo，隐藏暗色logo */
  .light-logo {
    display: block;
  }

  .dark-logo {
    display: none;
  }

  /* 暗色模式下显示暗色logo，隐藏亮色logo */
  .dark .light-logo {
    display: none;
  }

  .dark .dark-logo {
    display: block;
  }

  .home-icon:hover .logo-image {
    transform: scale(1.1);
  }

  .site-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--tw-color-primary, #2e405b);
    text-decoration: none;
  }

  /* 右侧操作区域 */
  .mobile-nav-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .hamburger-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--tw-color-primary, #2e405b);
    cursor: pointer;
    padding: 0.5rem;
  }

  /* 弹出式菜单 */
  .mobile-menu {
    display: none;
    position: fixed;
    top: 3.5rem; /* 调整位置，与新的导航栏高度匹配 */
    right: 1rem;
    width: 200px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    opacity: 0;
    transform: translateY(-10px);
    transition:
      opacity 0.2s ease,
      transform 0.2s ease;
  }

  .mobile-menu.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
  }

  .mobile-menu-content {
    padding: 0.75rem;
  }

  /* 背景遮罩 */
  .menu-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .menu-overlay.active {
    display: block;
    opacity: 1;
  }

  .mobile-menu-links {
    list-style: none;
    padding: 0;
    margin: 0 0 1rem 0;
  }

  .mobile-menu-item {
    margin-bottom: 0.25rem;
  }

  .mobile-menu-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    color: var(--tw-color-primary, #2e405b);
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .menu-icon {
    margin-right: 0.75rem;
    width: 1.25rem;
    height: 1.25rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .menu-text {
    flex: 1;
  }

  .mobile-menu-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  /* 社交媒体图标 */
  .mobile-social-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  .mobile-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--tw-color-primary, #2e405b);
    transition: all 0.2s ease;
  }

  .mobile-social-link:hover {
    transform: translateY(-2px);
    background-color: rgba(0, 0, 0, 0.1);
  }

  .social-icon {
    width: 1.25rem;
    height: 1.25rem;
  }

  /* 暗色模式适配 */
  .dark .mobile-nav-header {
    background-color: #232222;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .dark .mobile-menu {
    background-color: #232222;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .dark .home-icon,
  .dark .site-title,
  .dark .hamburger-button,
  .dark .mobile-menu-link {
    color: #e5e7eb;
  }

  .dark .mobile-menu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .dark .mobile-social-links {
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  .dark .mobile-social-link {
    background-color: rgba(255, 255, 255, 0.1);
    color: #e5e7eb;
  }

  .dark .mobile-social-link:hover {
    background-color: rgba(255, 255, 255, 0.15);
  }

  /* 响应式设计 - 在移动设备上显示 */
  @media (max-width: 1023px) {
    .mobile-nav-container {
      display: block;
    }
  }
</style>

<script>
  // 使用立即执行函数确保作用域隔离
  ;(function () {
    // 初始化移动导航功能
    function initMobileNavigation() {
      const menuToggle = document.getElementById('mobile-menu-toggle')
      const mobileMenu = document.getElementById('mobile-menu')
      const menuOverlay = document.getElementById('menu-overlay')

      // 检查必要元素
      if (!menuToggle || !mobileMenu || !menuOverlay) {
        return
      }

      // 切换菜单显示/隐藏
      menuToggle.addEventListener('click', (event) => {
        event.stopPropagation() // 阻止事件冒泡
        mobileMenu.classList.toggle('active')
        menuOverlay.classList.toggle('active')

        // 切换汉堡按钮图标
        const icon = menuToggle.querySelector('span')
        if (icon) {
          // 确保使用完整的图标类名
          if (mobileMenu.classList.contains('active')) {
            icon.className = 'i-mdi-close' // 菜单打开时显示关闭图标
          } else {
            icon.className = 'i-mdi-menu' // 菜单关闭时显示菜单图标
          }
        }
      })

      // 点击背景遮罩关闭菜单
      menuOverlay.addEventListener('click', () => {
        mobileMenu.classList.remove('active')
        menuOverlay.classList.remove('active')
        const icon = menuToggle.querySelector('span')
        if (icon) {
          // 确保使用完整的图标类名
          icon.className = 'i-mdi-menu'
        }
      })

      // 点击菜单项后关闭菜单
      const menuLinks = mobileMenu.querySelectorAll('.mobile-menu-link')
      menuLinks.forEach((link) => {
        link.addEventListener('click', () => {
          mobileMenu.classList.remove('active')
          menuOverlay.classList.remove('active')
          const icon = menuToggle.querySelector('span')
          if (icon) {
            // 确保使用完整的图标类名
            icon.className = 'i-mdi-menu'
          }
        })
      })

      // 阻止菜单内部点击事件冒泡
      mobileMenu.addEventListener('click', (event) => {
        event.stopPropagation()
      })

      // 使用事件委托处理点击其他区域关闭菜单
      document.addEventListener('click', () => {
        if (mobileMenu.classList.contains('active')) {
          mobileMenu.classList.remove('active')
          menuOverlay.classList.remove('active')
          const icon = menuToggle.querySelector('span')
          if (icon) {
            // 确保使用完整的图标类名
            icon.className = 'i-mdi-menu'
          }
        }
      })
    }

    // 在DOM内容加载后初始化
    document.addEventListener('DOMContentLoaded', initMobileNavigation)
  })()
</script>
