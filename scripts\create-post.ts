import { execSync } from 'node:child_process'
import fs from 'node:fs'
import path from 'node:path'
import consola from 'consola'
import dayjs from 'dayjs'

// 模板路径
const TEMPLATE_PATH = './src/content/templates/post-template-simple.md'
const FULL_TEMPLATE_PATH = './src/content/templates/post-template.md'

createPost()

/**
 * Create a new post.
 * Prompts the user for a file name and extension, and creates a new post file with frontmatter.
 * If successful, opens the new post file in the default editor.
 */
async function createPost(): Promise<void> {
  consola.start('Ready to create a new post!')

  const filename: string = await consola.prompt('Enter file name: ', { type: 'text' })
  const extension: string = await consola.prompt('Select file extension: ', { type: 'select', options: ['.md', '.mdx'] })
  const isDraft: boolean = await consola.prompt('Is this a draft?', { type: 'confirm', initial: true })

  const targetDir = './src/content/posts/'
  const fullPath: string = path.join(targetDir, `${filename}${extension}`)

  // 检查是否使用完整模板
  const useFullTemplate: boolean = await consola.prompt('Use full template with all fields?', { type: 'confirm', initial: false })
  const templatePath = useFullTemplate ? FULL_TEMPLATE_PATH : TEMPLATE_PATH

  // 检查模板是否存在
  if (!fs.existsSync(templatePath)) {
    consola.warn(`Template not found at ${templatePath}. Using default frontmatter.`)
    const frontmatter = getFrontmatter({
      title: filename,
      pubDate: dayjs().format('YYYY-MM-DD'),
      categories: '[]',
      description: '\'\'',
      slug: filename.toLowerCase().replace(/\s+/g, '-'),
      draft: isDraft ? 'true' : 'false',
    })

    try {
      fs.writeFileSync(fullPath, frontmatter)
      consola.success('New post created successfully!')
    }
    catch (error) {
      consola.error((error as Error).message || 'Failed to create new post!')
      return
    }
  } else {
    // 使用模板文件
    try {
      // 读取模板内容
      let templateContent = fs.readFileSync(templatePath, 'utf8')

      // 替换模板中的标题和日期
      templateContent = templateContent.replace(/title: "\u6587\u7ae0\u6807\u9898"/, `title: "${filename}"`)
      templateContent = templateContent.replace(/pubDate: \d{4}-\d{2}-\d{2}/, `pubDate: ${dayjs().format('YYYY-MM-DD')}`)
      templateContent = templateContent.replace(/draft: false/, `draft: ${isDraft}`)

      // 写入新文件
      fs.writeFileSync(fullPath, templateContent)
      consola.success('New post created successfully using template!')
    }
    catch (error) {
      consola.error((error as Error).message || 'Failed to create new post!')
      return
    }
  }

  const open: boolean = await consola.prompt('Open the new post?', { type: 'confirm', initial: true })
  if (open) {
    consola.info(`Opening ${fullPath}...`)
    execSync(`code "${fullPath}"`)
  }
}

/**
 * Create frontmatter from a data object.
 * @param data The data object to convert to frontmatter.
 * @returns The frontmatter as a string.
 */
function getFrontmatter(data: { [key: string]: string }): string {
  const frontmatter = Object.entries(data)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n')

  return `---\n${frontmatter}\n---`
}
