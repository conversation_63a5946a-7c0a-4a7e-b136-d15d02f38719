---
import { themeConfig } from '~/.config'

const { navLinks, socialLinks } = themeConfig.site
const { translate: t } = Astro.locals
---

<nav class="text-center font-bold" flex="~ col gap-4">
  <ul lg="flex-col items-start text-4" class="text-3.5" flex="~ row gap-2 justify-center">
    {
      navLinks.map((nav) => (
        <li>
          <a class="" href={nav.href}>
            {t(nav.name)}
          </a>
        </li>
      ))
    }
  </ul>
  <ul flex="~ row gap-1 justify-center">
    {
      socialLinks.map(({ href, name, title }) => {
        // 生成图标类名
        let iconClass = name ? `i-mdi-${name}` : 'i-mdi-link-variant'

        return (
          <li>
            <a
              href={href}
              target="_blank"
              title={title || name}
              class="not-underline-hover inline-flex items-center social-link"
            >
              <span class:list={[iconClass, 'w-6 h-6']} />
            </a>
          </li>
        )
      })
    }
  </ul>

  <style>
    .social-link {
      transition:
        transform 0.2s ease,
        opacity 0.2s ease;
    }

    .social-link:hover {
      transform: translateY(-2px);
      opacity: 0.8;
    }
  </style>
</nav>
