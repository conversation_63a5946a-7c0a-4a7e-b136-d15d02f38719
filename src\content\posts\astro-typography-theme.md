---
title: 使用 Astro 和 Typography 主题创建博客
pubDate: 2025-04-13
categories: ["教程", "Astro"]
description: "本文介绍如何使用 Astro 框架和 Typography 主题创建一个美观的个人博客。"
---

# 使用 Astro 和 Typography 主题创建博客

在这篇文章中，我将分享如何使用 Astro 框架和 Typography 主题创建一个美观的个人博客。

## Astro 简介

[Astro](https://astro.build/) 是一个现代的静态站点生成器，它允许你使用你喜欢的 UI 组件（React、Vue、Svelte 等）来构建更快的网站。Astro 的特点包括：

- 零 JavaScript 运行时（默认情况下）
- 服务器优先的 API 设计
- 强大的内容集合系统
- 丰富的集成生态系统

## Typography 主题

[Typography](https://github.com/moeyua/astro-theme-typography) 是一个专注于排版的 Astro 博客主题，它是从 Hexo 主题 [活版印字](https://github.com/sumimakito/hexo-theme-typography) 移植而来的。这个主题的特点包括：

- 使用 **Astro**、**TypeScript** 和 **UnoCSS** 构建
- **快速**。100% [Pagespeed 得分](https://pagespeed.web.dev/analysis/https-astro-theme-typography-vercel-app/j34nq9tx0s?form_factor=desktop)
- **排版**。源自流行的中文排版规范，旨在为网站访问者提供更好的阅读体验
- **响应式**。响应式设计，在所有屏幕尺寸上都表现良好
- **无障碍**。精心设计的语义和无障碍内容
- **SEO 友好**。支持 Open Graph 和 Twitter Cards，提供更好的社交分享体验
- **站点地图**和 **RSS feed**，方便搜索引擎抓取
- 支持 i18n
- 支持 Disqus、Giscus、Twikoo 作为评论服务
- 支持暗黑模式

## 安装步骤

### 1. 克隆仓库

```bash
git clone https://github.com/moeyua/astro-theme-typography.git my-blog
```

### 2. 安装依赖

```bash
cd my-blog
npm install
```

### 3. 自定义配置

编辑 `src/.config/user.ts` 文件来覆盖默认配置：

```typescript
import type { UserConfig } from '~/types'

export const userConfig: Partial<UserConfig> = {
  // 站点基本信息
  site: { 
    title: "我的博客",
    subtitle: "My Blog",
    author: "博主",
    description: "使用Astro和Typography主题创建的个人博客",
    website: "https://myblog.com",
    // 其他配置...
  },
  // 外观设置
  appearance: {
    theme: "system", // 'light' | 'dark' | 'system'
    locale: "zh-cn"
  }
}
```

### 4. 添加文章

在 `src/content/posts` 目录下创建 Markdown 文件：

```markdown
---
title: 文章标题
pubDate: 2025-04-13
categories: ["分类1", "分类2"]
description: "文章描述"
---

文章内容...
```

### 5. 本地开发

```bash
npm run dev
```

### 6. 构建和部署

```bash
npm run build
```

然后可以将 `dist` 目录部署到你喜欢的托管服务，如 Vercel、Netlify 等。

## 结语

使用 Astro 和 Typography 主题创建博客非常简单，而且可以获得出色的性能和用户体验。希望这篇教程对你有所帮助！

如果你有任何问题或建议，欢迎在评论区留言。
