/*
 * 代码块复制按钮样式
 * 为代码块复制功能提供样式支持
 */

/* 代码块容器样式 */
.code-container {
  position: relative !important;
  overflow: visible !important; /* 确保按钮不会被裁剪 */
}

/* 复制按钮基础样式 */
.copy-button {
  position: absolute !important;
  top: 0.5rem !important;
  right: 0.5rem !important;
  width: 1.75rem !important;
  height: 1.75rem !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  border-radius: 0.25rem !important;
  background: rgba(0, 0, 0, 0.2) !important; /* 默认添加轻微背景 */
  border: none !important;
  cursor: pointer !important;
  padding: 0 !important;
  color: white !important;
  opacity: 0.7 !important;
  transition: all 0.2s ease !important;
  z-index: 50 !important; /* 提高z-index确保按钮在最上层 */
  pointer-events: auto !important; /* 确保按钮可点击 */
  visibility: visible !important; /* 确保按钮可见 */
}

.copy-button:hover {
  background-color: #30363d !important;
  opacity: 1 !important;
}

.copy-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5) !important;
}

/* 暗色模式适配 */
.dark .copy-button {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

.dark .copy-button:hover {
  background-color: rgba(255, 255, 255, 0.25) !important;
}

/* 复制成功提示 */
.copy-success {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) scale(0.9) !important;
  background-color: rgba(0, 0, 0, 0.85) !important;
  color: white !important;
  padding: 1rem !important;
  border-radius: 50% !important;
  z-index: 1000 !important;
  opacity: 0 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 60px !important;
  height: 60px !important;
  -webkit-backdrop-filter: blur(4px) !important; /* Safari 兼容性 */
  backdrop-filter: blur(4px) !important;
}

.dark .copy-success {
  background-color: rgba(255, 255, 255, 0.85) !important;
  color: #222 !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3) !important;
}

.copy-success span {
  font-size: 2rem !important;
}

.copy-success.show {
  opacity: 1 !important;
  transform: translate(-50%, -50%) scale(1) !important;
}
