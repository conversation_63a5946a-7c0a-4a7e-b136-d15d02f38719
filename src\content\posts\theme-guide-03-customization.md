---
title: "川流主题使用教程（三）：主题定制"
pubDate: 2023-12-27
categories: ["教程"]
tags: ["川流主题", "Astro", "博客", "主题定制"]
description: "川流主题使用教程的第三篇，介绍如何设置网站基本信息、自定义颜色和样式、配置导航菜单等。"
series:
  name: "川流主题使用教程"
  order: 3
---

# 川流主题使用教程（三）：主题定制

在[上一篇教程](/posts/theme-guide-02-content)中，我们介绍了如何管理博客内容。本篇教程将重点讲解如何定制川流主题的外观，包括设置网站基本信息、自定义颜色和样式、配置导航菜单等。

## 配置文件概述

川流主题的配置系统分为两部分：

1. **默认配置**：位于 `src/.config/default.ts`，包含主题的默认设置
2. **用户配置**：位于 `src/.config/user.ts`，用于覆盖默认设置

您只需要修改 `user.ts` 文件，不建议直接修改 `default.ts`。

配置文件使用 TypeScript 格式，但即使您不熟悉 TypeScript，也可以轻松修改配置。

## 网站基本信息设置

在 `src/.config/user.ts` 中，`site` 部分包含网站的基本信息：

```typescript
site: {
  title: '川流',                // 网站标题
  subtitle: '一切为了自由',      // 网站副标题
  author: '番茄',              // 作者名称
  description: '川流,自由,信念,理想,Flow, freedom, belief, ideal', // 网站描述
  website: 'https://example.com/', // 网站URL（重要！）
  pageSize: 5,                 // 每页显示的文章数量
  // 其他设置...
},
```

### 重要字段说明

- **title**：显示在浏览器标签页和网站头部的主标题
- **subtitle**：显示在网站头部的副标题
- **author**：文章的默认作者名称
- **description**：用于SEO的网站描述，应包含关键词
- **website**：您的网站URL，影响站点地图和其他功能
- **pageSize**：首页和分类页面每页显示的文章数量

## 社交链接配置

在 `site` 部分中，`socialLinks` 数组用于配置显示在网站页脚的社交媒体链接：

```typescript
socialLinks: [
  {
    name: 'github',
    href: 'https://github.com/yourusername',
  },
  {
    name: 'twitter',
    href: 'https://twitter.com/yourusername',
  },
  {
    name: 'telegram',
    href: 'https://t.me/yourusername',
  },
  {
    name: 'email',
    href: 'mailto:<EMAIL>',
  },
  // 可以添加更多社交链接...
],
```

## 导航菜单配置

在 `site` 部分中，`navLinks` 数组用于配置网站顶部的导航菜单：

```typescript
navLinks: [
  {
    name: '首页',
    href: '/',
  },
  {
    name: '归档',
    href: '/archive',
  },
  {
    name: '分类',
    href: '/categories',
  },
  {
    name: '标签',
    href: '/tags',
  },
  {
    name: '系列',
    href: '/series',
  },
  {
    name: '友链',
    href: '/links',
  },
  {
    name: '搜索',
    href: '/search',
  },
  {
    name: '关于',
    href: '/about',
  },
  // 可以添加更多导航链接...
],
```

## 页脚配置

在 `site` 部分中，`footer` 数组用于配置网站页脚的文本：

```typescript
footer: [
  '© %year <a target="_blank" href="%website">%author</a>', // 第一行
  '一切为了自由', // 第二行
  // 可以添加更多行...
],
```

### 特殊占位符

页脚文本支持以下占位符：

- **%year**：当前年份
- **%website**：网站URL（来自site.website）
- **%author**：作者名称（来自site.author）

## 颜色和样式自定义

在 `appearance` 部分，您可以自定义网站的颜色和主题：

```typescript
appearance: {
  theme: 'system', // 'light' | 'dark' | 'system'
  locale: 'zh-cn',
  colorsLight: {
    primary: '#2e405b',   // 主色调（亮色模式）
    background: '#ffffff', // 背景色（亮色模式）
  },
  colorsDark: {
    primary: '#FFFFFF',   // 主色调（暗色模式）
    background: '#232222', // 背景色（暗色模式）
  },
  fonts: {
    header: '"HiraMinProN-W6","Source Han Serif CN","Source Han Serif SC","Source Han Serif TC",serif', // 标题字体
    ui: '"Source Sans Pro","Roboto","Helvetica","Helvetica Neue","Source Han Sans SC","Source Han Sans TC","PingFang SC","PingFang HK","PingFang TC",sans-serif', // 界面字体
  },
},
```

### 主题模式

`theme` 字段控制网站的默认主题模式：

- **light**：始终使用亮色模式
- **dark**：始终使用暗色模式
- **system**：根据用户系统设置自动切换（推荐）

### 自定义颜色

您可以修改 `colorsLight` 和 `colorsDark` 中的颜色值来自定义网站的配色方案：

- **primary**：主色调，用于链接、按钮等元素
- **background**：网站背景色

颜色值可以使用十六进制格式（如 `#2e405b`）或 CSS 颜色名称（如 `blue`）。

### 自定义字体

您可以修改 `fonts` 中的字体设置来自定义网站的字体：

- **header**：用于标题的字体
- **ui**：用于界面元素的字体

字体设置使用 CSS `font-family` 格式，可以指定多个字体作为备选。

## 分类和标签映射

在 `site` 部分中，`categoryMap` 和 `tagMap` 数组用于将中文分类和标签名映射为英文URL：

```typescript
// 分类映射表
categoryMap: [
  { name: '技术', path: 'technology' },
  { name: '生活', path: 'life' },
  { name: '思考', path: 'thoughts' },
  // 可以添加更多映射...
],

// 标签映射表
tagMap: [
  { name: '编程', path: 'programming' },
  { name: '旅行', path: 'travel' },
  { name: '阅读', path: 'reading' },
  // 可以添加更多映射...
],
```

这些映射表有两个作用：

1. 将中文分类/标签名转换为对SEO更友好的英文URL
2. 确保分类/标签名的一致性

## 国际化设置

在 `appearance` 部分，`locale` 字段用于设置网站的语言：

```typescript
locale: 'zh-cn', // 简体中文
```

川流主题支持多种语言，包括：

- `zh-cn`：简体中文
- `zh-tw`：繁体中文
- `en-us`：英文（美国）
- `ja-jp`：日文
- `it-it`：意大利文

## 下一步

在下一篇教程中，我们将介绍[功能模块配置](/posts/theme-guide-04-features)，包括如何设置评论系统、搜索功能、阅读进度条、广告管理等功能。

敬请期待！
