---
import { getCollection } from 'astro:content';
import { formatDate } from '~/utils';
import { themeConfig } from '~/.config';

// 从配置文件获取相关文章设置
const relatedPostsConfig = themeConfig.relatedPosts;

// 如果组件有传入的属性，优先使用组件属性，否则使用配置文件中的设置
const {
  currentPost,
  maxPosts = relatedPostsConfig.maxPosts,
  randomize = relatedPostsConfig.randomize,
  enable = relatedPostsConfig.enable
} = Astro.props;

// 如果禁用了相关文章功能，直接返回空
if (!enable) return null;

const allPosts = await getCollection('posts');

// 查找相关文章（同类别或标签）
let matchedPosts = allPosts
  .filter(post => post.id !== currentPost.id) // 排除当前文章
  .filter(post => {
    // 匹配分类
    const sameCategories = currentPost.data.categories?.some(
      (cat: string) => post.data.categories?.includes(cat)
    );

    // 匹配标签
    const sameTags = currentPost.data.tags && post.data.tags &&
      currentPost.data.tags.some((tag: string) => post.data.tags?.includes(tag));

    return sameCategories || sameTags;
  });

// 如果启用随机显示且匹配的文章数量超过要显示的数量
if (randomize && matchedPosts.length > maxPosts) {
  // 随机打乱数组
  matchedPosts = matchedPosts.sort(() => Math.random() - 0.5);
}

// 截取需要显示的文章数量
const relatedPosts = matchedPosts.slice(0, maxPosts);
---

{relatedPosts.length > 0 && (
  <div class="related-posts">
    <h3>相关推荐</h3>
    <ul>
      {relatedPosts.map(post => (
        <li>
          <a href={post.data.slug ? `/posts/${post.data.slug}/` : `/posts/${post.id}/`}>{post.data.title}</a>
          <span class="post-date">{formatDate(post.data.pubDate)}</span>
        </li>
      ))}
    </ul>
  </div>
)}

<style>
  .related-posts {
    margin-top: 2rem;
    padding: 1rem;
    border: 1px solid #eaeaea;
    border-radius: 0.5rem;
  }

  .dark .related-posts {
    border-color: #333;
  }

  .related-posts h3 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
  }

  .related-posts ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .related-posts li {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
  }

  .post-date {
    font-size: 0.85rem;
    color: #666;
  }

  .dark .post-date {
    color: #aaa;
  }
</style>
