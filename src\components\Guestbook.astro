---
import Twikoo from '~/components/comments/Twikoo.astro'

interface Props {
  title?: string;
}

const {
  title = "留言板"
} = Astro.props;
---

<div class="guestbook-container tablet-centered">
  <div class="guestbook-header">
    <h1 class="page-title">{title}</h1>
  </div>

  <div class="guestbook-content">
    <div class="guestbook-welcome content-card">
      <div class="welcome-icon">
        <span class="i-mdi-message-text-outline"></span>
      </div>
      <div class="welcome-text">
        <h2 class="card-title">欢迎留言</h2>
        <p>欢迎来到我的留言板！这里是一个交流互动的地方，您可以在下方留言，分享您的想法、提出建议、提问或者只是打个招呼。</p>
        <p>我会定期查看并回复留言，感谢您的访问和支持！</p>
      </div>
    </div>

    <div class="guestbook-comments-card content-card">
      <h2 class="comments-title">留言区</h2>
      <div class="comments-container">
        <Twikoo path="/guestbook" />
      </div>
    </div>
  </div>
</div>

<style>
  .guestbook-container {
    max-width: 100%;
    animation: fadeIn 0.8s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .guestbook-header {
    text-align: center;
    margin-bottom: 2.5rem;
  }

  .guestbook-intro {
    max-width: 700px;
    margin: 0 auto;
    color: #666;
  }

  .dark .guestbook-intro {
    color: #aaa;
  }

  /* 留言板卡片样式已移至card-responsive.css */
  .guestbook-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .welcome-text {
    flex: 1;
  }

  .welcome-text p {
    margin-bottom: 0.75rem;
    line-height: 1.6;
  }



  .comments-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--uno-colors-primary, #2e405b);
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #eaeaea;
  }

  .dark .comments-title {
    border-bottom-color: #333;
  }

  .comments-container {
    margin-top: 1rem;
  }
</style>
