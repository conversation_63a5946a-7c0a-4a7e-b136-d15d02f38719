---
import { themeConfig } from '~/.config'
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import SiteSeo from '~/components/SiteSeo.astro'
import type { Post } from '~/types'
import { formatDate, getTags, getPathFromTag } from '~/utils'

export async function getStaticPaths() {
  // 每页显示的文章数量
  const postsPerPage = 10

  const tagMap = await getTags()
  const paths = []

  // 为每个标签的每个页面创建路径
  for (const [tagName, tagPosts] of tagMap.entries()) {
    // 使用标签映射表生成URL友好的路径
    const urlName = getPathFromTag(tagName, themeConfig.site.tagMap)

    // 按发布日期排序（新的在前）
    tagPosts.sort((a: Post, b: Post) => {
      return new Date(b.data.pubDate).getTime() - new Date(a.data.pubDate).getTime()
    })

    // 计算总页数
    const totalPosts = tagPosts.length
    const totalPages = Math.ceil(totalPosts / postsPerPage)

    // 为每个页面创建路径
    for (let page = 1; page <= totalPages; page++) {
      // 第一页使用基本路径，其他页面添加页码
      const pagePath = page === 1 ? urlName : `${urlName}/${page}`

      paths.push({
        params: { path: pagePath },
        props: {
          posts: tagPosts,
          name: tagName,
          currentPage: page,
          totalPages,
        },
      })
    }
  }

  return paths
}

// 每页显示的文章数量
const postsPerPage = 10

// 从props中获取数据
const { posts, name, currentPage, totalPages } = Astro.props
const { translate: t } = Astro.locals

// 获取当前页的文章
const startIndex = (currentPage - 1) * postsPerPage
const endIndex = Math.min(startIndex + postsPerPage, posts.length)
const currentPagePosts = posts.slice(startIndex, endIndex)

const title = `${name} - ${t('Tags')}${currentPage > 1 ? ` (第${currentPage}页)` : ''}`
const description = `${t('tag_count', posts.length)} - ${name} - 第${currentPage}页，共${totalPages}页`

// 为结构化数据准备文章列表项
const itemListElements = currentPagePosts.map((post, index) => ({
  '@type': 'ListItem',
  position: startIndex + index + 1,
  url: new URL(`posts/${post.data.slug || post.id}/`, Astro.site).toString(),
  name: post.data.title,
}))

// 生成分页按钮的数据
function generatePaginationData(totalPages: number, currentPage: number) {
  // 如果总页数小于等于7，返回所有页码
  if (totalPages <= 7) {
    return Array.from({ length: totalPages }, (_, i) => ({
      type: 'page',
      page: i + 1,
      isCurrent: i + 1 === currentPage,
    }))
  }

  // 否则，返回优化的分页导航数据
  const buttons = []

  // 始终显示第一页
  buttons.push({
    type: 'page',
    page: 1,
    isCurrent: currentPage === 1,
  })

  // 计算需要显示的页码范围
  let startPage = Math.max(2, currentPage - 1)
  let endPage = Math.min(totalPages - 1, currentPage + 1)

  // 调整以确保显示5个页码按钮（包括第一页和最后一页）
  if (currentPage <= 3) {
    // 靠近开始，显示2-5页
    endPage = Math.min(5, totalPages - 1)
  } else if (currentPage >= totalPages - 2) {
    // 靠近结束，显示倒数第5页到倒数第2页
    startPage = Math.max(2, totalPages - 4)
  }

  // 如果第一页和起始页之间有间隔，添加省略号
  if (startPage > 2) {
    buttons.push({ type: 'ellipsis' })
  }

  // 添加中间的页码
  for (let i = startPage; i <= endPage; i++) {
    buttons.push({
      type: 'page',
      page: i,
      isCurrent: i === currentPage,
    })
  }

  // 如果结束页和最后一页之间有间隔，添加省略号
  if (endPage < totalPages - 1) {
    buttons.push({ type: 'ellipsis' })
  }

  // 始终显示最后一页
  buttons.push({
    type: 'page',
    page: totalPages,
    isCurrent: currentPage === totalPages,
  })

  return buttons
}

// 生成页面URL
function getPageUrl(tagName: string, page: number) {
  const basePath = `/tags/${getPathFromTag(tagName, themeConfig.site.tagMap)}`
  return page === 1 ? `${basePath}/` : `${basePath}/${page}/`
}
---

<LayoutDefault>
  <SiteSeo slot="seo" title={title} desc={description} />

  <!-- 标签页结构化数据 -->
  <script
    is:inline
    type="application/ld+json"
    set:html={JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      headline: title,
      description: description,
      url: new URL(`tags/${getPathFromTag(name, themeConfig.site.tagMap)}/`, Astro.site).toString(),
      mainEntity: {
        '@type': 'ItemList',
        itemListElement: itemListElements,
      },
    })}
  />

  <div class="container">
    <h1 class="page-title">
      <span class="i-mdi-tag-outline tag-icon"></span>
      {name}
    </h1>

    <div class="tag-meta">
      <span>{posts.length} 篇文章</span>
      {
        currentPage > 1 && (
          <span>
            第 {currentPage} 页 / 共 {totalPages} 页
          </span>
        )
      }
    </div>

    <div class="tag-posts">
      <ul class="tag-posts-list">
        {
          currentPagePosts.map((post) => (
            <li>
              <a href={`/posts/${post.data.slug || post.id}/`}>{post.data.title}</a>
              <span class="post-date">{formatDate(post.data.pubDate)}</span>
            </li>
          ))
        }
      </ul>

      {
        totalPages > 1 && (
          <div class="tag-pages">
            {generatePaginationData(totalPages, currentPage).map((item) => {
              if (item.type === 'ellipsis') {
                return <span class="page-ellipsis">...</span>
              } else {
                return (
                  <a
                    href={getPageUrl(name, item.page as number)}
                    class={`page-link ${item.isCurrent ? 'current-page' : ''}`}
                  >
                    {item.page}
                  </a>
                )
              }
            })}
          </div>
        )
      }
    </div>

    <div class="back-to-tags">
      <a href="/tags" class="back-link">
        <span class="i-mdi-arrow-left back-icon"></span>
        返回标签列表
      </a>
    </div>
  </div>
</LayoutDefault>

<style>
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .page-title {
    margin-bottom: 1rem;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
  }

  .tag-icon {
    margin-right: 0.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }

  .tag-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
    color: var(--text-muted);
    font-size: 0.9rem;
  }

  .tag-posts-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .tag-posts-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color, #eee);
  }

  .tag-posts-list a {
    text-decoration: underline;
    text-underline-offset: 2px;
    color: var(--text-color, #333);
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .tag-posts-list a:hover {
    color: white;
    background-color: var(--tw-color-primary, #2e405b);
    transform: translateX(3px);
    display: inline-block;
    padding: 0 4px;
    border-radius: 3px;
  }

  .dark .tag-posts-list a:hover {
    color: var(--tw-color-primary, #ffffff);
    background-color: rgba(255, 255, 255, 0.1);
  }

  .post-date {
    color: var(--text-muted, #666);
    font-size: 0.85rem;
  }

  /* 分页样式 */
  .tag-pages {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 1.5rem 0;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color, #eee);
    flex-wrap: wrap;
  }

  .tag-pages a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2.25rem;
    height: 2.25rem;
    padding: 0 0.5rem;
    border-radius: 0.375rem;
    text-decoration: none;
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.05);
    color: var(--tw-color-primary, #2e405b);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.1);
  }

  .tag-pages a:hover {
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.1);
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    text-decoration: none;
  }

  .tag-pages a.current-page {
    background-color: var(--tw-color-primary, #2e405b);
    color: white;
    border-color: var(--tw-color-primary, #2e405b);
    font-weight: 500;
  }

  .tag-pages a.current-page:hover {
    background-color: var(--tw-color-primary, #2e405b);
    opacity: 0.9;
    color: white;
    transform: none;
    box-shadow: none;
  }

  .page-ellipsis {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2rem;
    height: 2rem;
    padding: 0 0.5rem;
    font-size: 0.875rem;
    color: var(--text-muted, #666);
  }

  .back-to-tags {
    margin-top: 2rem;
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-color, #333);
    font-size: 0.9rem;
    transition: all 0.2s ease;
  }

  .back-link:hover {
    color: white;
    background-color: var(--tw-color-primary, #2e405b);
    text-decoration: none;
    border-radius: 3px;
    padding: 0.3rem 0.5rem;
  }

  .dark .back-link:hover {
    color: var(--tw-color-primary, #ffffff);
    background-color: rgba(255, 255, 255, 0.1);
  }

  .back-icon {
    margin-right: 0.5rem;
  }

  /* 响应式设计 */
  @media (max-width: 640px) {
    .tag-posts-list li {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }
  }
</style>
