import { visit } from 'unist-util-visit'
import type { Root } from 'mdast'
import { themeConfig } from '../.config/index'

/**
 * 为 Markdown 内容中的图片添加优化处理的 remark 插件
 * 将标准的 Markdown 图片语法转换为使用 OptimizedImage 组件
 */
export function remarkOptimizedImages() {
  const imageConfig = themeConfig.imageOptimization || {}
  const enabled = imageConfig.enable !== false

  return (tree: Root) => {
    // 如果未启用图片优化，则只添加懒加载属性
    if (!enabled) {
      return remarkLazyImages()(tree)
    }

    // 首先检查 tree 是否有效
    if (!tree || typeof tree !== 'object') {
      return
    }

    try {
      // 访问所有图片节点
      visit(tree, 'image', (node) => {
        // 获取图片信息
        const url = node.url

        // 跳过外部图片（以http开头的URL）
        if (url.startsWith('http://') || url.startsWith('https://')) {
          // 为外部图片添加懒加载属性和灯箱触发类
          node.data = node.data || {}
          node.data.hProperties = node.data.hProperties || {}
          node.data.hProperties.loading = 'lazy'
          node.data.hProperties.class = `${node.data.hProperties.class || ''} lightbox-trigger`
          node.data.hProperties['data-original'] = url
          return
        }

        // 由于类型兼容性问题，我们不再尝试直接替换节点
        // 而是修改现有节点的属性，添加懒加载
        node.data = node.data || {}
        node.data.hProperties = node.data.hProperties || {}
        node.data.hProperties.loading = 'lazy'

        // 添加额外的类以便在客户端识别并优化
        node.data.hProperties.class = 'optimized-image lightbox-trigger'
        node.data.hProperties['data-original'] = url

        // 记录需要优化的图片，可以在未来扩展为构建时处理
        console.log(`图片需要优化: ${url}`);
      })
    } catch (error) {
      console.error('Error in remarkOptimizedImages plugin:', error)
    }
  }
}

/**
 * 为 Markdown 内容中的图片添加懒加载属性的 remark 插件
 * 这是一个简化版本，当完整的图片优化被禁用时使用
 */
export function remarkLazyImages() {
  return (tree: Root) => {
    // 首先检查 tree 是否有效
    if (!tree || typeof tree !== 'object') {
      return
    }

    try {
      // 访问所有图片节点
      visit(tree, 'image', (node) => {
        // 确保 node.data 存在
        node.data = node.data || {}

        // 确保 node.data.hProperties 存在
        node.data.hProperties = node.data.hProperties || {}

        // 添加 loading="lazy" 属性
        node.data.hProperties.loading = 'lazy'

        // 添加灯箱触发类
        node.data.hProperties.class = `${node.data.hProperties.class || ''} lightbox-trigger`
      })
    } catch (error) {
      console.error('Error in remarkLazyImages plugin:', error)
    }
  }
}
