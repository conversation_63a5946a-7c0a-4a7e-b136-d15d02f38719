---
import { getCollection } from 'astro:content';
import LayoutDefault from '~/layouts/LayoutDefault.astro';
import SiteSeo from '~/components/SiteSeo.astro';
import { formatDate, getPathFromSeries } from '~/utils';
import { themeConfig } from '~/.config';
import '../../styles/series-book-cards.css';

// 系列页面配置

// 每个系列默认显示的文章数量
const postsPerPage = 6;

// 获取所有非草稿文章
const allPosts = await getCollection('posts', ({ data }) => {
  return import.meta.env.PROD ? !data.draft : true;
});

// 获取状态的CSS类名
function getStatusClass(status: string): string {
  switch (status) {
    case '连载中':
      return 'ongoing';
    case '已完结':
      return 'completed';
    case '计划中':
      return 'planned';
    case '暂停更新':
      return 'paused';
    default:
      return 'ongoing';
  }
}

const seriesMap = new Map();

// 整理系列信息
allPosts.forEach(post => {
  if (post.data.series) {
    const { name, status } = post.data.series;
    if (!seriesMap.has(name)) {
      seriesMap.set(name, {
        name,
        posts: [],
        lastUpdated: new Date(0),
        status: status || '连载中' // 默认为连载中
      });
    }

    const series = seriesMap.get(name);
    series.posts.push(post);

    // 更新系列状态，使用最新文章的状态
    if (post.data.series.status) {
      series.status = post.data.series.status;
    }

    const postDate = new Date(post.data.modDate || post.data.pubDate);
    if (postDate > series.lastUpdated) {
      series.lastUpdated = postDate;
    }
  }
});

// 排序系列文章并准备分页数据
seriesMap.forEach(series => {
  // 排序文章
  series.posts.sort((a: any, b: any) => {
    if (!a.data.series || !b.data.series) return 0;
    return a.data.series.order - b.data.series.order;
  });

  // 计算分页信息
  series.totalPosts = series.posts.length;
  series.totalPages = Math.ceil(series.totalPosts / postsPerPage);
  series.currentPage = 1; // 默认显示第一页

  // 只显示第一页的文章
  series.visiblePosts = series.posts.slice(0, postsPerPage);
});

const seriesList = Array.from(seriesMap.values());
---

<LayoutDefault>
  <SiteSeo slot="seo" title="系列文章" desc="所有连载系列文章的索引" />

  <div class="container">
    <h1 class="page-title">系列文章</h1>

    <!-- 筛选导航 -->
    <div class="filter-container">
      <div class="filter-selects">
        <div class="filter-select-container">
          <label for="status-filter">按状态筛选：</label>
          <select id="status-filter" class="filter-select">
            <option value="all">全部</option>
            <option value="连载中">连载中</option>
            <option value="已完结">已完结</option>
            <option value="计划中">计划中</option>
            <option value="暂停更新">暂停更新</option>
          </select>
        </div>

        <div class="filter-select-container">
          <label for="time-filter">按时间筛选：</label>
          <select id="time-filter" class="filter-select">
            <option value="all">全部</option>
            <option value="latest">最新（1年内）</option>
            <option value="newer">较新（1-2年）</option>
            <option value="older">较早（2年以上）</option>
          </select>
        </div>

        <div class="filter-select-container">
          <label for="count-filter">按文章数量筛选：</label>
          <select id="count-filter" class="filter-select">
            <option value="all">全部</option>
            <option value="many">多篇（10篇以上）</option>
            <option value="medium">中等（5-9篇）</option>
            <option value="few">少量（1-4篇）</option>
          </select>
        </div>
      </div>

      <!-- 筛选结果提示 -->
      <div id="filter-result-message" class="filter-result-message" style="display: none;">
        没有找到符合条件的系列
      </div>
    </div>

    <div class="series-grid">
    {seriesList.map(series => (
      <a href={`/series/${getPathFromSeries(series.name, themeConfig.site.seriesMap)}/`}
         class="book-card"
         data-status={series.status}
         data-post-count={series.posts.length}
         data-last-updated={series.lastUpdated.toISOString()}
         aria-label={`查看${series.name}系列的所有文章`}>
        <div class="book-cover">
          <div class="book-content">
            <div class="book-header">
              <h2 class="book-title">
                {series.name}
              </h2>
              <span class={`book-status status-${getStatusClass(series.status)}`}>
                {series.status}
              </span>
            </div>

            <div class="book-meta">
              <div class="book-meta-item">
                <span class="book-meta-icon i-mdi-file-document-multiple"></span>
                <span>{series.posts.length} 篇文章</span>
              </div>
              <div class="book-meta-item">
                <span class="book-meta-icon i-mdi-calendar-clock"></span>
                <span>最后更新: {formatDate(series.lastUpdated)}</span>
              </div>
            </div>
          </div>

          <div class="book-decoration i-mdi-book-open-page-variant"></div>
        </div>
      </a>
    ))}
    </div>
  </div>
</LayoutDefault>

<script is:inline define:vars={{ seriesList, postsPerPage }}>
  // 优化的数据加载：只存储必要的数据
  const allSeriesData = {};

  try {
    // 初始化筛选功能
    function initSeriesFilter() {
      // 获取下拉框元素
      const statusFilter = document.getElementById('status-filter');
      const timeFilter = document.getElementById('time-filter');
      const countFilter = document.getElementById('count-filter');

      // 获取系列项和结果消息
      const seriesItems = document.querySelectorAll('.book-card');
      const resultMessage = document.getElementById('filter-result-message');

      // 应用筛选函数
      function applyFilters() {
        const selectedStatus = statusFilter.value;
        const selectedTime = timeFilter.value;
        const selectedCount = countFilter.value;

        let visibleCount = 0;

        seriesItems.forEach(item => {
          // 获取系列属性
          const status = item.getAttribute('data-status');
          const lastUpdated = new Date(item.getAttribute('data-last-updated'));
          const postCount = parseInt(item.getAttribute('data-post-count'), 10);

          // 检查状态筛选
          const statusMatch = selectedStatus === 'all' || status === selectedStatus;

          // 检查时间筛选
          let timeMatch = true;
          const currentYear = new Date().getFullYear();
          const yearDiff = currentYear - lastUpdated.getFullYear();

          if (selectedTime === 'latest') {
            timeMatch = yearDiff < 1;
          } else if (selectedTime === 'newer') {
            timeMatch = yearDiff >= 1 && yearDiff < 2;
          } else if (selectedTime === 'older') {
            timeMatch = yearDiff >= 2;
          }

          // 检查文章数量筛选
          let countMatch = true;
          if (selectedCount === 'many') {
            countMatch = postCount >= 10;
          } else if (selectedCount === 'medium') {
            countMatch = postCount >= 5 && postCount < 10;
          } else if (selectedCount === 'few') {
            countMatch = postCount < 5;
          }

          // 应用筛选结果
          if (statusMatch && timeMatch && countMatch) {
            item.style.display = 'block';
            visibleCount++;
          } else {
            item.style.display = 'none';
          }
        });

        // 显示或隐藏结果消息
        if (visibleCount === 0) {
          resultMessage.style.display = 'block';
        } else {
          resultMessage.style.display = 'none';
        }
      }

      // 添加事件监听器
      statusFilter.addEventListener('change', applyFilters);
      timeFilter.addEventListener('change', applyFilters);
      countFilter.addEventListener('change', applyFilters);
    }

    // 页面加载时初始化筛选功能
    document.addEventListener('DOMContentLoaded', initSeriesFilter);
    // 处理每个系列的数据
    seriesList.forEach(series => {
      // 将每个系列的所有文章转换为简单的对象数组，只保留必要字段
      const postsData = series.posts.map(post => ({
        id: post.id,
        title: post.data.title,
        order: post.data.series?.order || 0,
        pubDate: post.data.pubDate
      }));

      // 存储系列数据
      allSeriesData[series.name] = postsData;
    });

    // 不再需要客户端分页处理，因为我们使用了服务端分页
  } catch (error) {
    console.error('初始化系列分页时出错:', error);
  }
</script>

<style is:global>
  .page-title {
    margin-bottom: 2rem;
    text-align: center;
  }

  /* 筛选导航样式 */
  .series-filter {
    margin-bottom: 2rem;
    padding: 1rem;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .dark .series-filter {
    background-color: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .filter-selects {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .filter-select-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    min-width: 200px;
  }

  .filter-select-container label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #666;
  }

  .dark .filter-select-container label {
    color: #aaa;
  }

  .filter-select {
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #eaeaea;
    background-color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .dark .filter-select {
    background-color: #333;
    border-color: #444;
    color: #eaeaea;
  }

  .filter-select:hover {
    border-color: var(--tw-color-primary, #2e405b);
  }

  .filter-select:focus {
    outline: none;
    border-color: var(--tw-color-primary, #2e405b);
    box-shadow: 0 0 0 2px rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.2);
  }

  .filter-result-message {
    margin-top: 1rem;
    padding: 1rem;
    text-align: center;
    color: #666;
    font-style: italic;
  }

  .dark .filter-result-message {
    color: #aaa;
  }

  /* 响应式设计 - 筛选导航 */
  @media (max-width: 768px) {
    .filter-selects {
      flex-direction: row; /* 保持水平排列 */
      flex-wrap: nowrap; /* 防止换行 */
      gap: 0.5rem; /* 减小间距 */
    }

    .filter-select-container {
      min-width: auto; /* 移除最小宽度限制 */
      width: 33.33%; /* 平均分配宽度 */
    }

    .filter-select-container label {
      font-size: 0.8rem; /* 减小标签字体 */
      white-space: nowrap; /* 防止标签换行 */
    }

    .filter-select {
      padding: 0.35rem; /* 减小内边距 */
      font-size: 0.8rem; /* 减小字体大小 */
    }
  }

  /* 更小屏幕的额外优化 */
  @media (max-width: 480px) {
    .filter-selects {
      gap: 0.25rem; /* 进一步减小间距 */
    }

    .filter-select-container label {
      font-size: 0.75rem; /* 进一步减小标签字体 */
    }

    .filter-select {
      padding: 0.25rem; /* 进一步减小内边距 */
      font-size: 0.75rem; /* 进一步减小字体大小 */
    }
  }

  /* 保留筛选器样式，其他样式已移至series-book-cards.css */
</style>
