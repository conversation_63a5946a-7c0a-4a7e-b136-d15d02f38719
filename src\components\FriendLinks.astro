---

import Twikoo from '~/components/comments/Twikoo.astro'

export interface FriendLink {
  name: string;
  url: string;
  avatar?: string;
  description: string;
  category?: string;
}

// 友链数据
export const friendLinks: FriendLink[] = [
  {
    name: "示例博客1",
    url: "https://example.com",
    description: "一个示例博客",
    category: "技术"
  },
  {
    name: "示例博客2",
    url: "https://example.org",
    description: "另一个示例博客",
    category: "生活"
  },
  {
    name: "示例博客3",
    url: "https://example.net",
    description: "第三个示例博客",
    category: "技术"
  },
  {
    name: "示例博客4",
    url: "https://example.io",
    description: "第四个示例博客",
    category: "设计"
  },
  {
    name: "示例博客5",
    url: "https://example.dev",
    description: "第五个示例博客",
    category: "生活"
  },
  {
    name: "示例博客6",
    url: "https://example.app",
    description: "第六个示例博客",
    category: "设计"
  }
];

// 获取所有分类
const categories = [...new Set(friendLinks.map(link => link.category))].filter(Boolean) as string[];

const {
  title = "友情链接"
} = Astro.props;
---

<div class="friend-links-container tablet-centered">
  <h1 class="page-title">{title}</h1>

  {categories.length > 0 && (
    <div class="friend-links-categories">
      <button class="category-btn active" data-category="all">全部</button>
      {categories.map(category => (
        <button class="category-btn" data-category={category}>{category}</button>
      ))}
    </div>
  )}

  <div class="friend-links-grid">
    {friendLinks.map(link => (
      <a
        href={link.url}
        target="_blank"
        rel="noopener noreferrer"
        class="friend-link-card not-underline-hover"
        data-category={link.category || ""}
      >
        <div class="friend-link-avatar">
          {link.avatar ? (
            <img src={link.avatar} alt={link.name} loading="lazy" />
          ) : (
            <div class="avatar-placeholder">
              <span class="i-mdi-account"></span>
            </div>
          )}
        </div>
        <div class="friend-link-info">
          <h3 class="friend-link-name">{link.name}</h3>
          <p class="friend-link-description">{link.description}</p>
          {link.category && <span class="friend-link-category">{link.category}</span>}
        </div>
      </a>
    ))}
  </div>

  <div class="friend-links-apply content-card">
    <h2 class="card-title">申请友链</h2>
    <p>如果您想与我交换友链，请按以下格式在评论区留言：</p>
    <pre class="apply-format">
名称：您的网站名称
链接：您的网站链接
描述：简短的描述
头像：头像链接（可选）
    </pre>
  </div>

  <div class="friend-links-comments content-card">
    <Twikoo path="/links" />
  </div>
</div>

<style>
  .friend-links-container {
    max-width: 100%;
    margin: 0 auto;
  }

  .friend-links-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: bold;
    text-align: center;
  }

  .friend-links-description {
    margin-bottom: 2rem;
    color: #666;
    text-align: center;
  }

  .dark .friend-links-description {
    color: #aaa;
  }

  .friend-links-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 2rem;
    justify-content: center;
  }

  .category-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 20px;
    background-color: #f0f0f0;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .category-btn:hover {
    background-color: #e0e0e0;
  }

  .category-btn.active {
    background-color: var(--tw-color-primary, #2e405b);
    color: white;
  }

  .dark .category-btn {
    background-color: #333;
    color: #e5e7eb;
  }

  .dark .category-btn:hover {
    background-color: #444;
  }

  .dark .category-btn.active {
    background-color: var(--tw-color-primary, #FFFFFF);
    color: #232222;
  }

  /* 友链卡片样式已移至card-responsive.css */
  .friend-link-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e0e0e0;
    color: #666;
  }

  .dark .avatar-placeholder {
    background-color: #444;
    color: #e5e7eb;
  }

  .friend-link-info {
    flex: 1;
  }

  .friend-link-category {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    background-color: rgba(46, 64, 91, 0.1);
    color: var(--tw-color-primary, #2e405b);
    font-size: 0.8rem;
  }

  .dark .friend-link-category {
    background-color: rgba(255, 255, 255, 0.1);
    color: #e5e7eb;
  }

  .apply-format {
    padding: 1rem;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-family: monospace;
    white-space: pre-wrap;
    margin-top: 1rem;
  }

  .dark .apply-format {
    background-color: #333;
  }

  .friend-links-comments {
    margin-top: 2rem;
  }
</style>

<script is:inline defer>
  // 使用立即执行函数确保作用域隔离
  (function() {
    // 分类筛选功能
    function initFriendLinksFilter() {
      const categoryButtons = document.querySelectorAll('.category-btn');
      const friendCards = document.querySelectorAll('.friend-link-card');

      if (categoryButtons.length > 0 && friendCards.length > 0) {
        categoryButtons.forEach(button => {

          button.addEventListener('click', () => {
            // 更新按钮状态
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // 获取选中的分类
            const selectedCategory = button.getAttribute('data-category');

            // 筛选友链卡片
            friendCards.forEach(card => {
              if (selectedCategory === 'all') {
                card.style.display = 'flex';
              } else {
                const cardCategory = card.getAttribute('data-category');
                card.style.display = cardCategory === selectedCategory ? 'flex' : 'none';
              }
            });
          });
        });


      }
    }

    // 只在DOMContentLoaded事件时初始化一次
    document.addEventListener('DOMContentLoaded', initFriendLinksFilter);
  })();
</script>
