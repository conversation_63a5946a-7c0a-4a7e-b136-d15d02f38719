/**
 * 通用卡片和响应式样式
 * 用于关于页、留言板和友链页面
 */

/* 通用卡片样式 */
.content-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.content-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.dark .content-card {
  background-color: #222;
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.dark .content-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 卡片标题 */
.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--tw-color-primary, #2e405b);
}

.dark .card-title {
  color: #e5e7eb;
}

/* 卡片内容 */
.card-content {
  line-height: 1.6;
}

/* 关于页卡片 */
.about-card {
  overflow: hidden;
}

.about-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--tw-color-primary, #2e405b), #4a6baf);
}

/* 留言板卡片 */
.guestbook-welcome {
  display: flex;
  gap: 1rem;
}

.welcome-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, var(--tw-color-primary, #2e405b), #4a6baf);
  border-radius: 50%;
  color: white;
  font-size: 1.5rem;
}

.welcome-text h2 {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
}



/* 友链卡片 */
.friend-link-card {
  display: flex;
  align-items: center;
  padding: 0.875rem;
  border-radius: 8px;
  background-color: #f9f9f9;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.friend-link-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.dark .friend-link-card {
  background-color: #222;
  border-color: rgba(255, 255, 255, 0.05);
}

.dark .friend-link-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.friend-link-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.friend-link-name {
  font-size: 1rem;
  margin: 0 0 0.25rem 0;
  font-weight: 600;
}

.friend-link-description {
  font-size: 0.85rem;
  margin: 0 0 0.25rem 0;
  color: #666;
}

.dark .friend-link-description {
  color: #aaa;
}

.friend-links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

/* 响应式样式 */

/* 移动设备 (480px - 767px) */
@media (max-width: 767px) {
  .content-card {
    padding: 0.875rem;
    margin-bottom: 1rem;
  }

  .card-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  /* 关于页 */
  .about-header {
    margin-bottom: 1.25rem;
  }

  .about-meta {
    font-size: 0.85rem;
  }

  /* 留言板 */
  .guestbook-welcome {
    flex-direction: column;
  }

  .welcome-icon {
    margin-bottom: 0.75rem;
  }

  /* 友链 */
  .friend-links-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.875rem;
  }
}

/* 小屏幕设备 (小于480px) */
@media (max-width: 479px) {
  .content-card {
    padding: 0.75rem;
  }

  .card-title {
    font-size: 1rem;
  }

  /* 留言板 */
  .welcome-icon {
    width: 40px;
    height: 40px;
  }

  .welcome-text h2 {
    font-size: 1.2rem;
  }

  /* 友链 */
  .friend-links-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .friend-link-avatar {
    width: 40px;
    height: 40px;
    margin-right: 0.5rem;
  }

  .friend-link-name {
    font-size: 0.9rem;
  }

  .friend-link-description {
    font-size: 0.8rem;
  }
}

/* 超小屏幕设备 (小于360px) */
@media (max-width: 359px) {
  .friend-links-grid {
    grid-template-columns: 1fr;
  }
}
