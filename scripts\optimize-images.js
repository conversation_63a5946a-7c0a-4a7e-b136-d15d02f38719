/**
 * 图片优化构建脚本
 * 用于在构建前优化静态图片
 *
 * 功能：
 * 1. 压缩图片
 * 2. 生成WebP和AVIF格式
 * 3. 生成响应式尺寸
 */

import fs from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import sharp from 'sharp'

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const rootDir = path.resolve(__dirname, '..')

// 配置
const config = {
  // 源图片目录
  sourceDirs: [
    path.join(rootDir, 'public', 'images'),
    path.join(rootDir, 'src', 'assets'),
  ],
  // 输出目录
  outputDir: path.join(rootDir, 'public', 'optimized-images'),
  // 图片格式 - 与.config/user.ts中的配置保持一致
  formats: ['webp', 'avif'],
  // 图片质量 - 与.config/user.ts中的配置保持一致
  quality: 85,
  // 响应式尺寸 - 与.config/user.ts中的配置保持一致
  widths: [480, 640, 768, 1024, 1366, 1600, 1920],
  // 文件类型
  extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  // 是否覆盖已存在的文件
  overwrite: true, // 设置为true以确保配置更改后重新生成所有图片
}

/**
 * 递归获取目录中的所有图片文件
 * @param {string} dir - 目录路径
 * @returns {Promise<string[]>} - 图片文件路径数组
 */
async function getImageFiles(dir) {
  const files = await fs.readdir(dir, { withFileTypes: true })
  const imageFiles = []

  for (const file of files) {
    const filePath = path.join(dir, file.name)

    if (file.isDirectory()) {
      // 递归处理子目录
      const subDirImages = await getImageFiles(filePath)
      imageFiles.push(...subDirImages)
    }
    else {
      // 检查文件扩展名
      const ext = path.extname(file.name).toLowerCase()
      if (config.extensions.includes(ext)) {
        imageFiles.push(filePath)
      }
    }
  }

  return imageFiles
}

/**
 * 优化单个图片
 * @param {string} imagePath - 图片路径
 */
async function optimizeImage(imagePath) {
  try {
    // 解析文件信息
    const parsedPath = path.parse(imagePath)
    const relativePath = path.relative(rootDir, imagePath)
    const outputBasePath = path.join(config.outputDir, relativePath.replace(parsedPath.ext, ''))

    // 创建输出目录
    await fs.mkdir(path.dirname(outputBasePath), { recursive: true })

    // 读取图片
    const image = sharp(imagePath)
    const metadata = await image.metadata()

    // 处理每种格式和尺寸
    for (const format of config.formats) {
      for (const width of config.widths) {
        // 如果原图小于目标宽度，跳过
        if (metadata.width && metadata.width < width) {
          continue
        }

        // 构建输出文件路径
        const outputPath = `${outputBasePath}-${width}.${format}`

        // 检查文件是否已存在
        if (!config.overwrite) {
          try {
            await fs.access(outputPath)
            console.log(`跳过已存在的文件: ${outputPath}`)
            continue
          }
          catch (error) {
            // 文件不存在，继续处理
          }
        }

        // 调整图片大小并转换格式
        let processedImage = image.clone().resize(width)

        // 根据格式设置输出选项
        switch (format) {
          case 'webp':
            processedImage = processedImage.webp({ quality: config.quality })
            break
          case 'avif':
            processedImage = processedImage.avif({ quality: config.quality })
            break
          case 'jpeg':
            processedImage = processedImage.jpeg({ quality: config.quality })
            break
          default:
            processedImage = processedImage.toFormat(format, { quality: config.quality })
        }

        // 保存处理后的图片
        await processedImage.toFile(outputPath)
        console.log(`已优化: ${outputPath}`)
      }
    }
  }
  catch (error) {
    console.error(`处理图片 ${imagePath} 时出错:`, error)
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    // 创建输出目录
    await fs.mkdir(config.outputDir, { recursive: true })

    // 获取所有图片文件
    let allImageFiles = []
    for (const sourceDir of config.sourceDirs) {
      try {
        const imageFiles = await getImageFiles(sourceDir)
        allImageFiles = [...allImageFiles, ...imageFiles]
      }
      catch (error) {
        console.warn(`无法读取目录 ${sourceDir}:`, error.message)
      }
    }

    console.log(`找到 ${allImageFiles.length} 个图片文件`)

    // 处理所有图片
    const promises = allImageFiles.map(optimizeImage)
    await Promise.all(promises)

    console.log('图片优化完成!')
  }
  catch (error) {
    console.error('图片优化失败:', error)
    process.exit(1)
  }
}

// 执行主函数
main()
