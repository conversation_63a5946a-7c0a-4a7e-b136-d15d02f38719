# 川流主题

<h6 align='center'>
<a href="https://tomato1911.serv00.net/">在线预览</a>
</h6>

## 主题介绍

川流是一个优雅、高性能的博客主题，基于 Astro 构建，专为中文内容创作者设计。主题注重排版美学与阅读体验，同时提供丰富的功能与高度的可定制性。

### 主题特点

- **高性能**：基于 Astro 构建，生成静态网站，加载速度快
- **精美排版**：遵循中文排版规范，提供舒适的阅读体验
- **响应式设计**：完美适配桌面端、平板与移动设备
- **SEO 友好**：内置 Open Graph 和 Twitter Cards 支持，有利于社交分享
- **丰富功能**：支持文章分类、标签、系列、归档、搜索等功能
- **评论系统**：支持 Twikoo 等评论服务
- **暗色模式**：支持亮色/暗色/跟随系统三种模式
- **国际化**：支持多语言设置
- **赞赏功能**：支持多种赞赏方式

## 快速开始

### 环境要求

- **Node.js**：v18.0.0 或更高版本
- **包管理器**：pnpm

### 安装步骤

1. **克隆项目**：

```bash
# 使用 Git 克隆项目
git clone

# 进入项目目录
cd my-blog
```

2. **安装依赖**：

```bash
# 使用 pnpm
pnpm install
```

3. **启动开发服务器**：

```bash
pnpm run dev
```

启动后，访问 `http://localhost:4321` 查看您的网站。

### 构建网站

生成用于部署的静态文件：

```bash
pnpm run build
```

构建完成后，生成的文件将位于 `dist/` 目录中。

### 预览构建结果

在部署前预览构建结果：

```bash
pnpm run preview
```

## 内容管理

### 添加文章

在 `src/content/posts` 目录下创建新的 Markdown 文件，文件需要包含以下前置元数据：

```md
---
title: 文章标题
pubDate: 2023-01-01
categories: ["分类名称"]
tags: ["标签1", "标签2"]
description: "文章描述"
---

文章内容...
```

### 创建系列文章

要创建系列文章，在文章的前置元数据中添加 `series` 字段：

```md
---
title: 系列文章示例
pubDate: 2023-01-01
categories: ["教程"]
tags: ["Astro", "入门"]
description: "系列文章示例"
series: {
  name: "Astro教程",
  order: 1,
  status: "连载中" // 可选值：连载中、已完结、计划中、暂停更新
}
---
```

## 主题配置

川流主题高度可定制。配置文件位于 `src/.config/` 目录：

- `default.ts`：默认配置文件（不建议直接修改）
- `user.ts`：用户配置文件（在此处覆盖默认设置）

### 基本设置

在 `user.ts` 中配置网站基本信息：

```typescript
site: {
  title: '川流',                // 网站标题
  subtitle: '一切为了自由',      // 网站副标题
  author: '您的名字',           // 作者名称
  description: '网站描述',       // 网站描述
  website: 'https://example.com/', // 网站URL
  pageSize: 5,                 // 每页显示的文章数量
  // 其他设置...
},
```

### 导航菜单

配置网站导航菜单：

```typescript
navLinks: [
  {
    name: '首页',
    href: '/',
  },
  {
    name: '归档',
    href: '/posts',
  },
  {
    name: '分类',
    href: '/categories',
  },
  // 更多导航项...
],
```

### 社交链接

添加社交媒体链接：

```typescript
socialLinks: [
  {
    name: 'github',
    href: 'https://github.com/yourusername',
  },
  {
    name: 'twitter',
    href: 'https://twitter.com/yourusername',
  },
  // 更多社交链接...
],
```

### 外观设置

配置主题外观：

```typescript
appearance: {
  theme: 'light', // 'light' | 'dark' | 'system'
  locale: 'zh-cn', // 语言设置
},
```

### 评论系统

配置 Twikoo 评论系统：

```typescript
comment: {
  twikoo: {
    envId: 'your-twikoo-env-id', // Twikoo 环境 ID
    path: 'window.location.pathname',
  },
},
```

### 赞赏功能

配置赞赏功能：

```typescript
donate: {
  enable: true, // 启用赞赏功能
  paypal: 'https://www.paypal.me/yourusername', // PayPal 链接
  crypto: {
    btc: 'your-btc-address', // 比特币地址
    eth: 'your-eth-address', // 以太坊地址
    usdt: 'your-usdt-address', // USDT 地址
  },
},
```

## 常见问题

### 如何添加自定义页面？

在 `src/pages/` 目录下创建新的 `.astro` 文件：

```astro
---
// src/pages/custom-page.astro
import LayoutDefault from '../layouts/LayoutDefault.astro'
import SiteSeo from '../components/SiteSeo.astro'
---

<LayoutDefault>
  <SiteSeo slot="seo" title="自定义页面" desc="这是一个自定义页面" />

  <div class="custom-page">
    <h1>自定义页面</h1>
    <p>这是一个自定义页面的内容。</p>
  </div>
</LayoutDefault>
```

### 如何禁用某些功能？

大多数功能可以通过配置文件禁用：

```typescript
// 禁用评论
comment: {},

// 禁用赞赏
donate: {
  enable: false,
},
```

## 性能优化建议

1. **优化图片**：
   - 压缩图片大小
   - 使用适当的图片格式（WebP 优于 JPEG 和 PNG）
   - 考虑使用懒加载

## 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。
