---
import LayoutDefault from '../layouts/LayoutDefault.astro';
import OptimizedImage from '../components/OptimizedImage.astro';
---

<LayoutDefault>
  <div class="container mx-auto p-4">
    <h1 class="text-2xl font-bold mb-4">图片灯箱功能测试页面</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
      <div class="card p-4 border rounded">
        <h2 class="text-xl mb-2">标准图片（带灯箱）</h2>
        <img src="/images/logo-light.svg" alt="标准图片" width="200" height="200" class="lightbox-trigger" />
        <p class="mt-2 text-sm text-gray-600">点击图片可以打开灯箱查看大图</p>
      </div>
      
      <div class="card p-4 border rounded">
        <h2 class="text-xl mb-2">优化图片（带灯箱）</h2>
        <OptimizedImage src="/images/logo-light.svg" alt="优化图片" />
        <p class="mt-2 text-sm text-gray-600">点击图片可以打开灯箱查看大图</p>
      </div>
    </div>
    
    <div class="mb-8">
      <h2 class="text-xl font-bold mb-4">灯箱功能说明</h2>
      <ul class="list-disc pl-5 space-y-2">
        <li>点击任意图片可以打开灯箱查看大图</li>
        <li>使用左右箭头按钮或键盘方向键可以浏览图片</li>
        <li>使用 + 和 - 按钮或键盘 + 和 - 键可以放大缩小图片</li>
        <li>放大后可以拖动图片查看细节</li>
        <li>点击关闭按钮或按 ESC 键可以关闭灯箱</li>
      </ul>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="card p-4 border rounded">
        <h2 class="text-xl mb-2">图片 1</h2>
        <img src="https://picsum.photos/id/1/400/300" alt="随机图片 1" class="w-full h-auto lightbox-trigger" />
      </div>
      
      <div class="card p-4 border rounded">
        <h2 class="text-xl mb-2">图片 2</h2>
        <img src="https://picsum.photos/id/2/400/300" alt="随机图片 2" class="w-full h-auto lightbox-trigger" />
      </div>
      
      <div class="card p-4 border rounded">
        <h2 class="text-xl mb-2">图片 3</h2>
        <img src="https://picsum.photos/id/3/400/300" alt="随机图片 3" class="w-full h-auto lightbox-trigger" />
      </div>
    </div>
  </div>
</LayoutDefault>
