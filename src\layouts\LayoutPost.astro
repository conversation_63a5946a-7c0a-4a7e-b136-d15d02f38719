---
import PostMeta from '~/components/PostMeta.astro'
import type { Post } from '~/types'
import { themeConfig } from '~/.config'
import { getPathFromSeries } from '~/utils'

interface Props {
  post: Post
  isListItem?: boolean
}

const { post, isListItem = false } = Astro.props
---

<article class="prose">
  <PostMeta post={post} />

  <slot />
</article>

<!-- 只在非列表项（文章详情页）时添加结构化数据 -->
{
  !isListItem && (
    <script
      is:inline
      type="application/ld+json"
      set:html={JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'BlogPosting',
        headline: post.data.title,
        datePublished: post.data.pubDate,
        dateModified: post.data.modDate || post.data.pubDate,
        author: {
          '@type': 'Person',
          name: post.data.author || themeConfig.site.author,
        },
        description: post.data.description || '',
        keywords: post.data.tags?.join(', ') || '',
        image: post.data.banner ? new URL(post.data.banner.src, Astro.site).toString() : '',
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': new URL(`posts/${post.id}/`, Astro.site).toString(),
        },
        // 系列关系（如果是系列文章）
        ...(post.data.series
          ? {
              isPartOf: {
                '@type': 'Series',
                name: post.data.series.name,
                url: new URL(
                  `series/${getPathFromSeries(post.data.series.name, themeConfig.site.seriesMap)}/`,
                  Astro.site,
                ).toString(),
              },
              position: post.data.series.order,
            }
          : {}),
      })}
    />
  )
}
