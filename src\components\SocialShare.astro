---
interface Props {
  url: string
  title: string
  description?: string
}

const { url, title, description = '' } = Astro.props
const { translate: t } = Astro.locals

// 确保URL是完整的绝对URL
const fullUrl = url.startsWith('http') ? url : `${Astro.site}${url.startsWith('/') ? url.slice(1) : url}`
const encodedUrl = encodeURIComponent(fullUrl)
const encodedTitle = encodeURIComponent(title)
const encodedDescription = encodeURIComponent(description)

// 分享链接
const twitterUrl = `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`
const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`
const weiboUrl = `https://service.weibo.com/share/share.php?url=${encodedUrl}&title=${encodedTitle}`
const qqUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodedUrl}&title=${encodedTitle}&summary=${encodedDescription}`
const emailUrl = `mailto:?subject=${encodedTitle}&body=${encodedDescription}%0A${encodedUrl}`

// 分享文本
const shareText = t('share') || '分享到'
---

<div class="social-share">
  <div class="share-title">{shareText}</div>
  <div class="share-buttons">
    <!-- Twitter -->
    <a href={twitterUrl} target="_blank" rel="noopener noreferrer" class="share-button twitter" title="分享到Twitter">
      <span class="i-mdi-twitter"></span>
    </a>

    <!-- Facebook -->
    <a
      href={facebookUrl}
      target="_blank"
      rel="noopener noreferrer"
      class="share-button facebook"
      title="分享到Facebook"
    >
      <span class="i-mdi-facebook"></span>
    </a>

    <!-- 微博 -->
    <a href={weiboUrl} target="_blank" rel="noopener noreferrer" class="share-button weibo" title="分享到微博">
      <span class="i-mdi-sina-weibo"></span>
    </a>

    <!-- QQ -->
    <a href={qqUrl} target="_blank" rel="noopener noreferrer" class="share-button qq" title="分享到QQ">
      <span class="i-mdi-qqchat"></span>
    </a>

    <!-- 微信 -->
    <button class="share-button wechat" id="wechat-share" title="分享到微信">
      <span class="i-mdi-wechat"></span>
    </button>

    <!-- 电子邮件 -->
    <a href={emailUrl} class="share-button email" title="通过电子邮件分享">
      <span class="i-mdi-email"></span>
    </a>

    <!-- 复制链接 -->
    <button class="share-button copy-link" id="copy-link" title="复制链接">
      <span class="i-mdi-content-copy"></span>
    </button>
  </div>

  <!-- 微信分享弹窗 -->
  <div id="wechat-qrcode-modal" class="wechat-qrcode-modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h3>微信扫一扫分享</h3>
      <div id="qrcode"></div>
      <p>打开微信，点击底部的"发现"，使用"扫一扫"即可将网页分享到微信</p>
    </div>
  </div>
</div>

<style>
  .social-share {
    margin: 2rem 0;
    padding: 1rem;
    border-top: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
  }

  .dark .social-share {
    border-color: #333;
  }

  .share-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    color: var(--tw-color-primary, #2e405b);
  }

  .share-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .share-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: #f5f5f5;
    color: #555;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
  }

  .dark .share-button {
    background-color: #333;
    color: #eaeaea;
  }

  .share-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .share-button span {
    font-size: 1.25rem;
  }

  /* 社交媒体特定颜色 */
  .share-button.twitter:hover {
    background-color: #1da1f2;
    color: white;
  }

  .share-button.facebook:hover {
    background-color: #4267b2;
    color: white;
  }

  .share-button.weibo:hover {
    background-color: #e6162d;
    color: white;
  }

  .share-button.qq:hover {
    background-color: #12b7f5;
    color: white;
  }

  .share-button.wechat:hover {
    background-color: #07c160;
    color: white;
  }

  .share-button.email:hover {
    background-color: #ea4335;
    color: white;
  }

  .share-button.copy-link:hover {
    background-color: #6c5ce7;
    color: white;
  }

  /* 微信二维码弹窗 */
  .wechat-qrcode-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
  }

  .modal-content {
    background-color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    max-width: 90%;
    width: 300px;
    text-align: center;
    position: relative;
  }

  .dark .modal-content {
    background-color: #222;
    color: #eaeaea;
  }

  .close-modal {
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
  }

  #qrcode {
    margin: 1rem auto;
    width: 200px;
    height: 200px;
    background-color: white;
  }

  .dark #qrcode {
    background-color: white; /* QR码在暗色模式下仍需白色背景 */
  }

  .dark .modal-content p {
    color: #ccc;
  }

  /* 复制成功提示 */
  .copy-success {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 1rem;
    border-radius: 50%;
    z-index: 1000;
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    backdrop-filter: blur(4px);
  }

  .dark .copy-success {
    background-color: rgba(255, 255, 255, 0.85);
    color: #222;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  }

  .copy-success span {
    font-size: 2rem;
  }

  .copy-success.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  .copy-success {
    transform: translate(-50%, -50%) scale(0.9);
  }
</style>

<script is:inline defer>
  // 使用立即执行函数确保作用域隔离
  ;(function () {
    // 加载QRCode库
    function loadQRCodeLibrary() {
      if (typeof QRCode === 'undefined' && !document.querySelector('script[src*="qrcode.min.js"]')) {
        const script = document.createElement('script')
        script.src = 'https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js'
        document.head.appendChild(script)
      }
    }

    // 初始化社交分享功能
    function initSocialShare() {
      // 微信分享
      const wechatShareBtn = document.getElementById('wechat-share')
      const wechatModal = document.getElementById('wechat-qrcode-modal')
      const closeModal = document.querySelector('.close-modal')
      const qrcodeContainer = document.getElementById('qrcode')

      if (wechatShareBtn && wechatModal && closeModal && qrcodeContainer) {
        wechatShareBtn.addEventListener('click', () => {
          // 获取当前页面URL
          const url = window.location.href

          // 清空容器
          qrcodeContainer.innerHTML = ''

          // 如果QRCode库已加载
          if (typeof QRCode !== 'undefined') {
            // 创建一个canvas元素
            const canvas = document.createElement('canvas')
            qrcodeContainer.appendChild(canvas)

            // 生成二维码
            QRCode.toCanvas(canvas, url, { width: 200 }, (error) => {
              if (error) 
console.error(error)
            })
          }

          // 显示弹窗
          wechatModal.style.display = 'flex'
        })

        // 关闭弹窗
        closeModal.addEventListener('click', () => {
          wechatModal.style.display = 'none'
        })

        // 点击弹窗外部关闭
        wechatModal.addEventListener('click', (e) => {
          if (e.target === wechatModal) {
            wechatModal.style.display = 'none'
          }
        })
      }

      // 复制链接
      const copyLinkBtn = document.getElementById('copy-link')

      if (copyLinkBtn) {
        copyLinkBtn.addEventListener('click', async () => {
          const url = window.location.href

          try {
            await navigator.clipboard.writeText(url)

            // 显示复制成功提示
            const notification = document.createElement('div')
            notification.className = 'copy-success'

            // 创建成功图标
            const iconSpan = document.createElement('span')
            iconSpan.className = 'i-mdi-check-circle'
            notification.appendChild(iconSpan)

            document.body.appendChild(notification)

            // 显示提示
            setTimeout(() => {
              notification.classList.add('show')
            }, 10)

            // 2秒后隐藏提示
            setTimeout(() => {
              notification.classList.remove('show')
              setTimeout(() => {
                document.body.removeChild(notification)
              }, 300)
            }, 2000)

            // 改变复制按钮图标为成功图标
            const copyIcon = copyLinkBtn.querySelector('span')
            if (copyIcon) {
              // 存储原始类名
              const originalClass = copyIcon.className
              copyIcon.className = 'i-mdi-check'

              // 1.5秒后恢复原图标
              setTimeout(() => {
                copyIcon.className = originalClass
              }, 1500)
            }
          } catch (err) {
            console.error('无法复制链接:', err)
            alert('复制链接失败，请手动复制地址栏中的URL')
          }
        })
      }
    }

    // 在DOM内容加载后初始化
    document.addEventListener('DOMContentLoaded', () => {
      loadQRCodeLibrary()
      initSocialShare()
    })
  })()
</script>
