---
import { themeConfig } from '~/.config'
import { getPathFromCategory } from '~/utils'

interface Props {
  category: string
}

const { category } = Astro.props

function getCategoryUrl(category: string) {
  return `/categories/${getPathFromCategory(category, themeConfig.site.categoryMap)}`
}
---

<a href={getCategoryUrl(category)} class="post-category">
  <span class="i-mdi-folder-outline category-icon"></span>
  <span class="category-text">{category}</span>
</a>

<style>
  .post-category {
    display: inline-flex;
    align-items: center;
    margin-right: 0.5rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    background-color: rgba(46, 64, 91, 0.1);
    color: var(--tw-color-primary, #2e405b);
    font-size: 0.85rem;
    text-decoration: none;
    transition: all 0.2s ease;
  }

  .post-category:hover {
    background-color: rgba(46, 64, 91, 0.2);
    text-decoration: none;
  }

  .category-icon {
    margin-right: 0.25rem;
    width: 0.9rem;
    height: 0.9rem;
  }

  .category-text {
    line-height: 1;
  }

  /* 暗黑模式 */
  .dark .post-category {
    background-color: rgba(255, 255, 255, 0.15);
    color: #e5e7eb;
  }

  .dark .post-category:hover {
    background-color: rgba(255, 255, 255, 0.25);
  }
</style>
