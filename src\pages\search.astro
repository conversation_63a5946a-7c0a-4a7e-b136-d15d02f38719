---
import { getCollection } from 'astro:content';
import LayoutDefault from '~/layouts/LayoutDefault.astro';
import SiteSeo from '~/components/SiteSeo.astro';

// 获取所有非草稿文章
const posts = await getCollection('posts', ({ data }) => {
  return import.meta.env.PROD ? !data.draft : true;
});

// 按发布日期排序（新的在前）
const sortedPosts = posts.sort((a, b) => {
  return new Date(b.data.pubDate).getTime() - new Date(a.data.pubDate).getTime();
});

// 将文章数据转换为简单的可搜索格式
const searchData = sortedPosts.map(post => ({
  id: post.id,
  title: post.data.title,
  description: post.data.description || '',
  pubDate: post.data.pubDate,
  categories: post.data.categories || [],
  // 使用新的URL生成逻辑
  url: post.data.slug ? `/posts/${post.data.slug}/` : `/posts/${post.id}/`,
  // 只取内容的前500个字符，避免数据过大
  content: post.body ? post.body.substring(0, 500) : ''
}));

// 将数据转换为JSON字符串
const searchDataJson = JSON.stringify(searchData);
---

<LayoutDefault>
  <SiteSeo slot="seo" title="搜索" desc="搜索博客内容" />

  <div class="search-page">
    <h1 class="page-title">搜索</h1>

    <div class="search-container">
      <div class="search-input-wrapper">
        <span class="search-icon">🔍</span>
        <input
          type="text"
          id="search-input"
          placeholder="搜索文章标题、内容、分类..."
          autocomplete="off"
        />
        <button id="search-clear" class="search-clear-btn" style="display: none;">✕</button>
      </div>
    </div>

    <div id="search-results" class="search-results">
      <p id="search-info" class="search-info">请输入关键词开始搜索</p>
      <div id="results-container" class="results-container"></div>
      <div id="pagination" class="pagination" style="display: none;">
        <button id="prev-page" class="pagination-btn">&laquo; 上一页</button>
        <span id="page-info" class="page-info">第 <span id="current-page">1</span> 页，共 <span id="total-pages">1</span> 页</span>
        <button id="next-page" class="pagination-btn">下一页 &raquo;</button>
        <div class="pagination-buttons">
          <button id="prev-page-mobile" class="pagination-btn" onclick="document.getElementById('prev-page').click();">&laquo; 上一页</button>
          <button id="next-page-mobile" class="pagination-btn" onclick="document.getElementById('next-page').click();">下一页 &raquo;</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入搜索结果增强脚本 -->
  <script is:inline src="/search-highlight.js"></script>
</LayoutDefault>

<style>
  .search-page {
    max-width: 100%;
  }

  /* 使用全局 .page-title 样式，移除自定义样式 */
  .search-title {
    margin-bottom: 1.5rem;
    font-weight: bold;
    text-align: center;
  }

  .search-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto 1.5rem;
  }

  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-icon {
    position: absolute;
    left: 12px;
    width: 20px;
    height: 20px;
    opacity: 0.7;
  }

  #search-input {
    width: 100%;
    padding: 10px 40px 10px 40px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: white;
  }

  #search-input:focus {
    outline: none;
    border-color: #2e405b;
    box-shadow: 0 0 0 2px rgba(46, 64, 91, 0.2);
  }

  .search-clear-btn {
    position: absolute;
    right: 12px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    color: #666;
    opacity: 0.7;
    transition: opacity 0.2s ease;
    background: none;
    border: none;
    font-size: 16px;
  }

  .search-clear-btn:hover {
    opacity: 1;
  }

  .search-info {
    text-align: center;
    margin: 2rem 0;
    color: #666;
  }

  .results-container {
    display: none;
    flex-direction: column;
    gap: 2rem;
  }

  /* 搜索结果项样式 */
  .search-result-item {
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .search-result-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }

  .result-date {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.5rem;
  }

  .result-title {
    font-size: 1.25rem;
    margin: 0 0 0.75rem 0;
    font-weight: bold;
  }

  .result-title a {
    color: #2e405b;
    text-decoration: none;
  }

  .result-title a:hover {
    text-decoration: underline;
  }

  .result-context {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #444;
    margin-bottom: 0.5rem;
  }



  /* 暗黑模式适配 */
  .dark #search-input {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e5e7eb;
  }

  .dark .search-icon,
  .dark .search-clear-btn {
    color: #e5e7eb;
  }

  .dark .search-result-item {
    border-color: #333;
  }

  .dark .search-result-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .dark .result-date {
    color: #aaa;
  }

  .dark .result-title a {
    color: #e5e7eb;
  }

  .dark .result-context {
    color: #bbb;
  }

  .dark .search-info {
    color: #aaa;
  }



  /* 分页样式 */
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2.5rem;
    gap: 1.5rem;
  }

  /* 按钮容器在标准布局下不显示 */
  .pagination-buttons {
    display: none;
  }

  .pagination-btn {
    padding: 0.75rem 1.25rem;
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.05);
    border: 1px solid rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.1);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    color: var(--uno-colors-primary, #2e405b);
  }

  .pagination-btn:hover {
    background-color: rgba(var(--uno-colors-primary-rgb, 46, 64, 91), 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .page-info {
    font-size: 0.95rem;
    color: var(--uno-colors-primary, #2e405b);
    opacity: 0.8;
  }

  .dark .pagination-btn {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: #e5e7eb;
  }

  .dark .pagination-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .dark .page-info {
    color: #e5e7eb;
    opacity: 0.8;
  }

  /* 响应式设计 */
  @media (max-width: 640px) {
    /* 页码信息在上，按钮在下且水平排列 */
    .pagination {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;
    }

    /* 页码信息样式 */
    .page-info {
      text-align: center;
      margin-bottom: 0.5rem;
      width: 100%;
    }

    /* 在移动端隐藏原来的按钮 */
    #pagination > .pagination-btn {
      display: none;
    }

    /* 水平排列按钮的容器 */
    .pagination-buttons {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 1rem;
      width: 100%;
    }

    /* 按钮样式 */
    .pagination-buttons .pagination-btn {
      display: inline-block;
      width: auto;
      min-width: 120px;
      max-width: 45%;
      text-align: center;
      padding: 0.5rem 1rem;
    }
  }
</style>

<script is:inline define:vars={{ searchDataJson }}>
// 解析搜索数据
const searchData = JSON.parse(searchDataJson);

// 分页相关变量
let currentPage = 1;
let totalPages = 1;
let allResults = [];
const resultsPerPage = 5; // 每页显示5条结果

// 在页面加载完成后初始化搜索功能
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search-input');
  const clearButton = document.getElementById('search-clear');
  const resultsContainer = document.getElementById('results-container');
  const searchInfo = document.getElementById('search-info');
  const pagination = document.getElementById('pagination');
  const prevPageBtn = document.getElementById('prev-page');
  const nextPageBtn = document.getElementById('next-page');
  const currentPageEl = document.getElementById('current-page');
  const totalPagesEl = document.getElementById('total-pages');

  if (!searchInput || !clearButton || !resultsContainer || !searchInfo || !pagination || !prevPageBtn || !nextPageBtn) {
    console.error('搜索元素未找到');
    return;
  }

  // 清除按钮点击事件
  clearButton.addEventListener('click', function() {
    searchInput.value = '';
    clearButton.style.display = 'none';
    searchInfo.textContent = '请输入关键词开始搜索';
    resultsContainer.innerHTML = '';
    resultsContainer.style.display = 'none';
    pagination.style.display = 'none';
    allResults = [];
    currentPage = 1;
  });

  // 上一页按钮点击事件
  prevPageBtn.addEventListener('click', function() {
    if (currentPage > 1) {
      currentPage--;
      renderPage();
    }
  });

  // 下一页按钮点击事件
  nextPageBtn.addEventListener('click', function() {
    if (currentPage < totalPages) {
      currentPage++;
      renderPage();
    }
  });

  // 输入框输入事件
  searchInput.addEventListener('input', function() {
    const query = this.value.trim();

    // 显示/隐藏清除按钮
    clearButton.style.display = query ? 'block' : 'none';

    if (!query) {
      searchInfo.textContent = '请输入关键词开始搜索';
      resultsContainer.innerHTML = '';
      resultsContainer.style.display = 'none';
      pagination.style.display = 'none';
      allResults = [];
      currentPage = 1;
      return;
    }

    // 执行搜索
    allResults = search(query);
    currentPage = 1; // 重置到第一页

    // 显示结果
    if (allResults.length === 0) {
      searchInfo.textContent = `未找到与 "${query}" 相关的结果`;
      resultsContainer.innerHTML = '';
      resultsContainer.style.display = 'none';
      pagination.style.display = 'none';
    } else {
      searchInfo.textContent = `找到 ${allResults.length} 个与 "${query}" 相关的结果`;
      totalPages = Math.ceil(allResults.length / resultsPerPage);
      renderPage();
      resultsContainer.style.display = 'flex';

      // 只有多于一页时才显示分页
      pagination.style.display = totalPages > 1 ? 'flex' : 'none';
    }
  });

  // 渲染当前页的结果
  function renderPage() {
    // 更新分页信息
    currentPageEl.textContent = currentPage;
    totalPagesEl.textContent = totalPages;

    // 禁用/启用分页按钮
    prevPageBtn.disabled = currentPage === 1;
    nextPageBtn.disabled = currentPage === totalPages;

    // 计算当前页的结果范围
    const startIndex = (currentPage - 1) * resultsPerPage;
    const endIndex = Math.min(startIndex + resultsPerPage, allResults.length);
    const currentResults = allResults.slice(startIndex, endIndex);

    // 渲染当前页的结果
    renderResults(currentResults, searchInput.value.trim());
  }

  // 从URL获取查询参数
  const urlParams = new URLSearchParams(window.location.search);
  const queryParam = urlParams.get('q');

  // 如果URL中有查询参数，填充搜索框并执行搜索
  if (queryParam) {
    searchInput.value = queryParam;
    // 触发input事件
    searchInput.dispatchEvent(new Event('input'));
  }
});

// 搜索函数
function search(query) {
  const normalizedQuery = query.toLowerCase();
  const results = [];

  for (const post of searchData) {
    if (
      post.title.toLowerCase().includes(normalizedQuery) ||
      post.description.toLowerCase().includes(normalizedQuery) ||
      post.content.toLowerCase().includes(normalizedQuery) ||
      (post.categories && post.categories.some(cat =>
        cat.toLowerCase().includes(normalizedQuery)
      ))
    ) {
      results.push(post);
    }
  }

  // 按相关性排序：标题匹配 > 分类匹配 > 描述匹配 > 内容匹配
  results.sort((a, b) => {
    const aTitle = a.title.toLowerCase().includes(normalizedQuery) ? 4 : 0;
    const aCategory = a.categories && a.categories.some(cat => cat.toLowerCase().includes(normalizedQuery)) ? 3 : 0;
    const aDescription = a.description.toLowerCase().includes(normalizedQuery) ? 2 : 0;
    const aContent = a.content.toLowerCase().includes(normalizedQuery) ? 1 : 0;

    const bTitle = b.title.toLowerCase().includes(normalizedQuery) ? 4 : 0;
    const bCategory = b.categories && b.categories.some(cat => cat.toLowerCase().includes(normalizedQuery)) ? 3 : 0;
    const bDescription = b.description.toLowerCase().includes(normalizedQuery) ? 2 : 0;
    const bContent = b.content.toLowerCase().includes(normalizedQuery) ? 1 : 0;

    const aScore = aTitle + aCategory + aDescription + aContent;
    const bScore = bTitle + bCategory + bDescription + bContent;

    return bScore - aScore;
  });

  return results;
}

// 渲染搜索结果
function renderResults(results, query) {
  console.log('Rendering search results with query:', query);
  const resultsContainer = document.getElementById('results-container');
  resultsContainer.innerHTML = '';

  results.forEach(post => {
    // 创建结果项容器
    const resultItem = document.createElement('div');
    resultItem.className = 'search-result-item';
    resultItem.style.padding = '1rem';
    resultItem.style.marginBottom = '1.5rem';
    resultItem.style.border = '1px solid #eaeaea';
    resultItem.style.borderRadius = '8px';
    resultItem.style.transition = 'all 0.3s ease';

    // 创建日期元素
    const dateElement = document.createElement('div');
    dateElement.style.fontSize = '0.85rem';
    dateElement.style.color = '#666';
    dateElement.style.marginBottom = '0.5rem';

    const date = new Date(post.pubDate);
    dateElement.textContent = date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    resultItem.appendChild(dateElement);

    // 创建标题元素
    const titleElement = document.createElement('h3');
    titleElement.style.fontSize = '1.25rem';
    titleElement.style.margin = '0 0 0.75rem 0';
    titleElement.style.fontWeight = 'bold';

    const titleLink = document.createElement('a');
    titleLink.href = post.url;
    titleLink.style.color = '#2e405b';
    titleLink.style.textDecoration = 'none';

    // 高亮标题中的关键词
    const highlightedTitle = highlightText(post.title, query);
    console.log('Highlighted title:', highlightedTitle);
    titleLink.innerHTML = highlightedTitle;

    titleElement.appendChild(titleLink);
    resultItem.appendChild(titleElement);

    // 创建上下文元素
    const contextElement = document.createElement('div');
    contextElement.style.fontSize = '0.95rem';
    contextElement.style.lineHeight = '1.6';
    contextElement.style.color = '#444';
    contextElement.style.marginBottom = '0.5rem';

    // 获取上下文片段
    const contextSnippet = getContextSnippet(post.content, query);

    // 高亮上下文中的关键词
    const highlightedContext = highlightText(contextSnippet, query);
    console.log('Highlighted context:', highlightedContext);
    contextElement.innerHTML = highlightedContext;

    resultItem.appendChild(contextElement);

    // 添加到结果容器
    resultsContainer.appendChild(resultItem);
  });
}



// 高亮匹配的文本
function highlightText(text, query) {
  if (!text || !query || query.trim() === '') return text || '';

  try {
    // 先将文本转换为字符串
    const textStr = String(text);

    // 转义正则表达式特殊字符
    const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // 创建正则表达式
    const regex = new RegExp(`(${escapedQuery})`, 'gi');

    // 使用内联样式替换所有匹配项
    return textStr.replace(regex, '<mark style="background-color: #ffdc00; color: #000; padding: 0 2px; border-radius: 2px; font-weight: bold;">$1</mark>');
  } catch (error) {
    console.error('Highlight error:', error);
    return text || '';
  }
}

// 截取包含搜索词的上下文
function getContextSnippet(content, query, maxLength = 200) {
  if (!content || !query) return '';

  const lowerContent = content.toLowerCase();
  const lowerQuery = query.toLowerCase();

  const index = lowerContent.indexOf(lowerQuery);
  if (index === -1) {
    // 如果没有找到关键词，返回内容的前一部分
    return content.substring(0, maxLength) + '...';
  }

  // 计算上下文的起始和结束位置
  const start = Math.max(0, index - 50);
  const end = Math.min(content.length, index + query.length + 50);

  // 提取上下文片段
  let snippet = content.substring(start, end);

  // 添加省略号
  if (start > 0) {
    snippet = '...' + snippet;
  }
  if (end < content.length) {
    snippet = snippet + '...';
  }

  return snippet;
}
</script>
