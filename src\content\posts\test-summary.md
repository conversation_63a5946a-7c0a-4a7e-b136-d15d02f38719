---
title: "测试自定义摘要功能"
pubDate: 2023-12-01
categories: ["测试"]
description: "这是一个简短的描述，主要用于SEO。"
summary: "这是一个更详细的摘要，将显示在文章列表中。这个摘要可以包含更多的信息，比如文章的主要内容、关键点等。这样可以让读者在浏览文章列表时，更好地了解文章的内容，从而决定是否点击阅读全文。"
tags: ["测试", "摘要", "自定义"]
---

# 测试自定义摘要功能

这是一篇测试文章，用于测试自定义摘要功能。

## 什么是自定义摘要？

自定义摘要是一种允许作者为文章提供专门用于列表显示的摘要内容的功能。与自动生成的摘要不同，自定义摘要可以更准确地概括文章内容，提高用户体验。

## 为什么需要自定义摘要？

有时候，文章的 `description` 字段可能比较简短，主要用于SEO优化，而不适合作为文章列表中的摘要显示。这时，我们可以使用 `summary` 字段提供一个更详细的摘要，专门用于文章列表显示。

## 如何使用自定义摘要？

在文章的前置数据（frontmatter）中添加 `summary` 字段即可：

```markdown
---
title: "文章标题"
description: "简短描述，用于SEO"
summary: "详细摘要，用于文章列表显示"
---
```

## 摘要显示优先级

1. 如果提供了 `summary` 字段，则使用 `summary` 作为摘要
2. 如果没有 `summary`，则从文章正文中提取前400个字符作为摘要

这样，`description` 字段将只用于SEO和元数据，不再用作文章列表的摘要。

## 结语

通过使用自定义摘要功能，我们可以更好地控制文章在列表中的显示效果，提高用户体验。
