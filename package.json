{"name": "chuan<PERSON>u", "type": "module", "version": "0.1.0", "packageManager": "pnpm@10.11.0", "scripts": {"dev": "astro check && astro dev", "build": "node scripts/optimize-images.js && astro check && astro build", "build:no-optimize": "astro check && astro build", "optimize-images": "node scripts/optimize-images.js", "preview": "astro preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "theme:create": "esno scripts/create-post.ts"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.3.5", "@astrojs/rss": "^4.0.12", "@astrojs/sitemap": "^3.5.1", "@types/mdast": "^4.0.4", "astro": "^5.13.7", "astro-robots-txt": "^1.0.0", "astro-seo": "^0.8.4", "giscus": "^1.6.0", "plaiceholder": "^3.0.0", "qrcode": "^1.5.4", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "sharp": "^0.34.3", "typescript": "~5.8.3", "unist-util-visit": "^5.0.0", "vite": "^6.3.6"}, "devDependencies": {"@antfu/eslint-config": "^4.19.0", "@iconify-json/mdi": "^1.2.3", "@types/markdown-it": "^14.1.2", "@types/node": "^22.18.3", "@types/qrcode": "^1.5.5", "@types/sanitize-html": "^2.16.0", "@unocss/eslint-plugin": "^66.5.1", "@unocss/preset-attributify": "^66.5.1", "@unocss/reset": "^66.5.1", "@unocss/transformer-directives": "^66.5.1", "astro-eslint-parser": "^1.2.2", "bumpp": "^10.2.3", "changelogen": "^0.6.2", "consola": "^3.4.2", "dayjs": "^1.11.18", "eslint": "^9.35.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-format": "^1.0.1", "esno": "^4.8.0", "lightningcss": "^1.30.1", "lint-staged": "^16.1.6", "markdown-it": "^14.1.0", "prettier-plugin-astro": "^0.14.1", "sanitize-html": "^2.17.0", "simple-git-hooks": "^2.13.1", "unocss": "^66.5.1", "unocss-preset-theme": "^0.14.1"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}