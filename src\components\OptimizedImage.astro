---
/**
 * OptimizedImage组件
 * 提供自动图片优化功能，包括：
 * 1. WebP格式转换
 * 2. 响应式图片尺寸
 * 3. 懒加载
 * 4. 模糊占位符
 */
import { getImage } from 'astro:assets';
import { getPlaiceholder } from 'plaiceholder';
import { themeConfig } from '../.config/index';

interface Props {
  src: ImageMetadata | string;
  alt: string;
  widths?: number[];
  sizes?: string;
  formats?: ('avif' | 'webp' | 'jpeg' | 'png' | 'svg')[];
  loading?: 'lazy' | 'eager';
  class?: string;
  style?: string;
  aspectRatio?: number;
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  position?: 'center' | 'top' | 'right' | 'bottom' | 'left';
  background?: string;
  quality?: number;
  usePlaceholder?: boolean;
}

// 获取图片优化配置
const imageConfig = themeConfig.imageOptimization || {};
const defaultQuality = imageConfig.quality || 80;
const defaultWidths = imageConfig.widths || [640, 750, 828, 1080, 1200, 1920];
const defaultFormats = imageConfig.formats || ['webp', 'jpeg'];
const usePlaceholderDefault = imageConfig.usePlaceholder !== false;

const {
  src,
  alt,
  widths = defaultWidths,
  sizes = '(min-width: 1024px) 1024px, 100vw',
  formats = defaultFormats,
  loading = 'lazy',
  class: className = '',
  style = '',
  aspectRatio,
  // 以下属性暂未使用，但保留以供未来扩展
  // fit = 'cover',
  // position = 'center',
  // background,
  quality = defaultQuality,
  usePlaceholder = usePlaceholderDefault,
} = Astro.props;

// 处理图片源
const isRemoteImage = typeof src === 'string';
interface OptimizedImage {
  src: string;
  format: string;
  width: number;
}
let optimizedImages: OptimizedImage[] = [];
let placeholder: string | null = null;

try {
  // 为每种格式和宽度生成优化图片
  for (const format of formats) {
    for (const width of widths) {
      if (isRemoteImage) {
        // 远程图片暂不支持优化，直接使用原图
        optimizedImages.push({
          src: src as string,
          format: format,
          width: width,
        });
        break; // 远程图片只添加一次
      }

      // 本地图片使用Astro的getImage进行优化
      const optimizedImage = await getImage({
        src: src as ImageMetadata,
        width: width,
        format: format,
        quality: quality,
      });
      optimizedImages.push({
        src: optimizedImage.src,
        format: format,
        width: width,
      });
    }
  }

  // 生成模糊占位符
  if (usePlaceholder && !isRemoteImage) {
    const smallImage = await getImage({
      src: src,
      width: 60,
      format: 'jpeg',
      quality: 30,
    });

    // 使用plaiceholder生成模糊占位符
    const buffer = await fetch(smallImage.src).then(res => res.arrayBuffer());
    const { base64 } = await getPlaiceholder(Buffer.from(buffer));
    placeholder = base64;
  }
} catch (error) {
  console.error('Error optimizing image:', error);
  // 出错时使用原始图片
  if (isRemoteImage) {
    optimizedImages = [{ src: src, format: 'jpeg', width: 0 }];
  } else {
    optimizedImages = [{ src: src.src, format: 'jpeg', width: 0 }];
  }
}

// 按格式和宽度分组
const imagesByFormat: Record<string, OptimizedImage[]> = {};
for (const format of formats) {
  imagesByFormat[format] = optimizedImages.filter(img => img.format === format);
}

// 构建srcset
const srcsets: Record<string, string> = {};
for (const [format, images] of Object.entries(imagesByFormat)) {
  if (images.length > 0) {
    srcsets[format] = images
      .map((img: OptimizedImage) => `${img.src} ${img.width}w`)
      .join(', ');
  }
}

// 获取默认图片
const defaultImage = optimizedImages.length > 0 ? optimizedImages[0].src : (isRemoteImage ? src : src.src);

// 构建内联样式，包括模糊占位符
let inlineStyle = style || '';
if (placeholder) {
  inlineStyle += `background-image: url(${placeholder}); background-size: cover; background-position: center;`;
}
if (aspectRatio) {
  inlineStyle += `aspect-ratio: ${aspectRatio};`;
}
---

<picture class={`optimized-image-container ${className}`}>
  {Object.entries(srcsets).map(([format, srcset]) => (
    <source type={`image/${format}`} srcset={srcset} sizes={sizes} />
  ))}

  <img
    src={defaultImage}
    alt={alt}
    loading={loading}
    class="optimized-image lightbox-trigger"
    style={inlineStyle}
    width={isRemoteImage ? undefined : src.width}
    height={isRemoteImage ? undefined : src.height}
    data-original={defaultImage}
  />
</picture>

<style>
  .optimized-image-container {
    display: block;
    width: 100%;
    overflow: hidden;
  }

  .optimized-image {
    width: 100%;
    height: auto;
    display: block;
    transition: filter 0.5s ease-out;
  }

  /* 图片加载时的模糊效果 */
  .optimized-image[loading="lazy"] {
    filter: blur(10px);
  }

  .optimized-image.loaded {
    filter: blur(0);
  }

  /* 灯箱触发样式 */
  .optimized-image.lightbox-trigger {
    cursor: zoom-in;
    transition: all 0.2s ease;
  }

  .optimized-image.lightbox-trigger:hover {
    opacity: 0.95;
  }
</style>

<script>
  // 图片加载完成后移除模糊效果
  document.addEventListener('DOMContentLoaded', () => {
    const images = document.querySelectorAll('.optimized-image');
    images.forEach(img => {
      if ((img as HTMLImageElement).complete) {
        img.classList.add('loaded');
      } else {
        img.addEventListener('load', () => {
          img.classList.add('loaded');
        });
      }
    });
  });
</script>
